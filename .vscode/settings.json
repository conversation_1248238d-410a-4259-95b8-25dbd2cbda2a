{
  "editor.tabSize": 2,
  "search.exclude": {
    "package-lock.json": true
  },
  "typescript.validate.enable": true,
  "editor.defaultFormatter": "dbaeumer.vscode-eslint",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.addMissingImports": "always",
    "source.fixAll.eslint": "always"
  },
  "eslint.workingDirectories": [{ "mode": "auto" }],
  // Multiple language settings for json and jsonc files
  "[json][jsonc]": {
    "editor.formatOnSave": false,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "editor.quickSuggestions": {
    "strings": true
  },
  "tailwindCSS.experimental.classRegex": [
    ["Variants \\=([^;]*);", "'([^']*)'"],
    ["Variants \\=([^;]*);", "\"([^\"]*)\""],
    ["Variants \\=([^;]*);", "\\`([^\\`]*)\\`"],
    ["Style \\=([^;]*);", "'([^']*)'"],
    ["Style \\=([^;]*);", "\"([^\"]*)\""],
    ["Style \\=([^;]*);", "\\`([^\\`]*)\\`"],
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ],
  "html.format.wrapAttributes": "auto",
  "cSpell.words": [
    "ASTAR",
    "autosize",
    "Avlb",
    "Cardano",
    "clsx",
    "countup",
    "daisyui",
    "dchange",
    "endtime",
    "flowbite",
    "gameplus",
    "iheart",
    "IMFA",
    "INFT",
    "IPFS",
    "hashgraph",
    "Leaderboard",
    "Mantine",
    "Moralis",
    "multicall",
    "nprogress",
    "persistor",
    "Presign",
    "qrcode",
    "shiba",
    "shibu",
    "Sider",
    "smartcontract",
    "solana",
    "tailwindcss",
    "twofa",
    "usehooks",
    "VRAR",
    "Wagmi",
    "webp"
  ],
  "[postcss]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[xml]": {
    "editor.defaultFormatter": "redhat.vscode-xml"
  },
  "[graphql]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "WillLuke.nextjs.hasPrompted": true,
  "[prisma]": {
    "editor.defaultFormatter": "Prisma.prisma"
  },
  "typescript.tsdk": "node_modules/typescript/lib"
}
