import { Logger } from '@nestjs/common';
import { registerAs } from '@nestjs/config';
import Joi = require('joi');

export const CHAIN_CONFIG_KEY = 'CHAIN_CONFIG_KEY';

export interface ChainConfig {
  debateBettingContractAddress: string;
  chainId: number;
  operatorPrivateKey: string;
  rpcEndpoint: string;
  blockTimeInMilliseconds: number;
}

export const chainConfigValidationSchema = Joi.object({
  DEBATE_BETTING_CONTRACT_ADDRESS: Joi.string().required(),
  CHAIN_ID: Joi.number().required(),
  OPERATOR_PRIVATE_KEY: Joi.string().required(),
  RPC_ENDPOINT: Joi.string().required(),
  BLOCK_TIME_IN_MILLISECONDS: Joi.number().required(),
});

export default registerAs(CHAIN_CONFIG_KEY, (): ChainConfig => {
  const { error, value } = chainConfigValidationSchema.validate(process.env, {
    abortEarly: false,
    stripUnknown: true,
  });

  if (error) {
    throw new Error(
      `Chain configuration validation error:\n${error.details
        .map((detail: { message: string }) => ` - ${detail.message}`)
        .join('\n')}`
    );
  }
  Logger.debug(`Chain configuration: ${JSON.stringify(value)}`);

  return {
    debateBettingContractAddress: value.DEBATE_BETTING_CONTRACT_ADDRESS,
    chainId: value.CHAIN_ID,
    operatorPrivateKey: value.OPERATOR_PRIVATE_KEY,
    rpcEndpoint: value.RPC_ENDPOINT,
    blockTimeInMilliseconds: value.BLOCK_TIME_IN_MILLISECONDS,
  };
});
