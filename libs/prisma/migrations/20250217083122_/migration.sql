/*
  Warnings:

  - The `onchain_status` column on the `BATTLE_Battle` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the `OnchainTransaction` table. If the table is not empty, all the data it contains will be lost.

*/
-- CreateEnum
CREATE TYPE "BattleCreatedOnchainStatus" AS ENUM ('Pending', 'Successful', 'Failed');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "EventType" AS ENUM ('DebateResolved', 'BetPlaced', 'UserClaimed', 'DebateCreated', 'DebateUpdated', 'DebateDeleted');

-- CreateEnum
CREATE TYPE "EventStatus" AS ENUM ('New', 'Confirmed', 'Processing', 'Completed', 'Failed');

-- DropIndex
DROP INDEX "BATTLE_Batt_agent1__a5bfa6_idx";

-- DropIndex
DROP INDEX "BATTLE_Batt_agent2__18c3f1_idx";

-- DropIndex
DROP INDEX "BATTLE_Batt_created_925e58_idx";

-- DropIndex
DROP INDEX "BATTLE_Batt_winner__b0844b_idx";

-- DropIndex
DROP INDEX "BATTLE_Battle_agent1_id_63ecb10f";

-- DropIndex
DROP INDEX "BATTLE_Battle_agent2_id_ba1a1228";

-- DropIndex
DROP INDEX "BATTLE_Battle_winner_id_66757c39";

-- AlterTable
ALTER TABLE "BATTLE_Battle" ADD COLUMN     "created_onchain_status" TEXT NOT NULL DEFAULT 'Pending',
ADD COLUMN     "deleted_at" TIMESTAMP(3),
ADD COLUMN     "platform_fee" DOUBLE PRECISION,
ADD COLUMN     "public_at" TIMESTAMP(3),
DROP COLUMN "onchain_status",
ADD COLUMN     "onchain_status" TEXT NOT NULL DEFAULT 'Pending';

-- AlterTable
ALTER TABLE "BATTLE_Event" ADD COLUMN     "processErrorMsg" TEXT,
ALTER COLUMN "status" SET DEFAULT 'New';

-- DropTable
DROP TABLE "OnchainTransaction";

-- CreateTable
CREATE TABLE "BATTLE_Battle_User_Bet" (
    "id" SERIAL NOT NULL,
    "bet_amount" DECIMAL(40,20) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "agent_id" INTEGER NOT NULL,
    "battle_id" INTEGER NOT NULL,
    "user_wallet" TEXT NOT NULL,
    "txn_hash" TEXT,

    CONSTRAINT "BATTLE_Battle_User_Bet_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BATTLE_User_Claim_History" (
    "id" SERIAL NOT NULL,
    "battle_id" INTEGER NOT NULL,
    "amount" DECIMAL(40,20) NOT NULL,
    "user_wallet" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BATTLE_User_Claim_History_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BATTLE_Onchain_Transaction" (
    "id" SERIAL NOT NULL,
    "chain_id" INTEGER NOT NULL,
    "contract_address" VARCHAR(255) NOT NULL,
    "txn_hash" VARCHAR(255),
    "txn_fee" BIGINT,
    "block_number" INTEGER,
    "block_timestamp" INTEGER,
    "block_hash" VARCHAR(255),
    "from_address" VARCHAR(255),
    "to_address" VARCHAR(255),
    "data" JSONB,
    "unsigned_raw" VARCHAR(255),
    "unsigned_txn_id" VARCHAR(255),
    "signed_raw" VARCHAR(255),
    "transfer_amount" BIGINT,
    "currency" VARCHAR(10),
    "method" VARCHAR(50),
    "type" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'InQueue',
    "error_message" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),
    "reference_id" INTEGER NOT NULL,

    CONSTRAINT "BATTLE_Onchain_Transaction_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "BATTLE_Onchain_Transaction_txn_hash_key" ON "BATTLE_Onchain_Transaction"("txn_hash");
