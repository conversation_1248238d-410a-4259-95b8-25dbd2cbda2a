-- CreateTable
CREATE TABLE "BATTLE_Event" (
    "id" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "smartContractAddress" TEXT NOT NULL,
    "txHash" TEXT NOT NULL,
    "returnValues" JSONB,
    "blockNumber" INTEGER NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL,
    "logIndex" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BATTLE_Event_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BATTLE_Battle" (
    "id" SERIAL NOT NULL,
    "topic" TEXT NOT NULL,
    "agent1_bet_count" INTEGER,
    "agent1_total_bet_amount" DOUBLE PRECISION,
    "agent2_bet_count" INTEGER,
    "agent2_total_bet_amount" DOUBLE PRECISION,
    "created_at" TIMESTAMP(3) NOT NULL,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "agent1_id" INTEGER NOT NULL,
    "agent2_id" INTEGER NOT NULL,
    "winner_id" INTEGER,
    "start_at" TIMESTAMP(3) NOT NULL,
    "duration" INTEGER NOT NULL,
    "judge_votes_agent1" JSONB,
    "judge_votes_agent2" JSONB,
    "document_urls" JSONB,
    "content" TEXT,

    CONSTRAINT "BATTLE_Battle_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "BATTLE_Event_txHash_key" ON "BATTLE_Event"("txHash");

-- CreateIndex
CREATE INDEX "BATTLE_Batt_agent1__a5bfa6_idx" ON "BATTLE_Battle"("agent1_id");

-- CreateIndex
CREATE INDEX "BATTLE_Batt_agent2__18c3f1_idx" ON "BATTLE_Battle"("agent2_id");

-- CreateIndex
CREATE INDEX "BATTLE_Batt_created_925e58_idx" ON "BATTLE_Battle"("created_at");

-- CreateIndex
CREATE INDEX "BATTLE_Batt_winner__b0844b_idx" ON "BATTLE_Battle"("winner_id");

-- CreateIndex
CREATE INDEX "BATTLE_Battle_agent1_id_63ecb10f" ON "BATTLE_Battle"("agent1_id");

-- CreateIndex
CREATE INDEX "BATTLE_Battle_agent2_id_ba1a1228" ON "BATTLE_Battle"("agent2_id");

-- CreateIndex
CREATE INDEX "BATTLE_Battle_winner_id_66757c39" ON "BATTLE_Battle"("winner_id");
