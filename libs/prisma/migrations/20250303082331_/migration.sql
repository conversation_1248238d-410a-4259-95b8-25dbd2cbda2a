/*
  Warnings:

  - You are about to drop the column `agent1_bet_count` on the `BATTLE_Battle` table. All the data in the column will be lost.
  - You are about to drop the column `agent1_total_bet_amount` on the `BATTLE_Battle` table. All the data in the column will be lost.
  - You are about to drop the column `agent2_bet_count` on the `BATTLE_Battle` table. All the data in the column will be lost.
  - You are about to drop the column `agent2_total_bet_amount` on the `BATTLE_Battle` table. All the data in the column will be lost.
  - You are about to drop the column `judge_votes_agent1` on the `BATTLE_Battle` table. All the data in the column will be lost.
  - You are about to drop the column `judge_votes_agent2` on the `BATTLE_Battle` table. All the data in the column will be lost.
  - You are about to drop the column `onchain_status` on the `BATTLE_Battle` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "BATTLE_Battle" DROP COLUMN "agent1_bet_count",
DROP COLUMN "agent1_total_bet_amount",
DROP COLUMN "agent2_bet_count",
DROP COLUMN "agent2_total_bet_amount",
DROP COLUMN "judge_votes_agent1",
DROP COLUMN "judge_votes_agent2",
DROP COLUMN "onchain_status",
ADD COLUMN     "resolved_onchain_status" TEXT NOT NULL DEFAULT 'Pending';
