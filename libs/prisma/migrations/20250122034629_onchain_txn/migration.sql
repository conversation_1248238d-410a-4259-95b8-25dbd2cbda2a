-- CreateEnum
CREATE TYPE "OnChainTransactionStatus" AS ENUM ('InQueue', 'Processing', 'Sent', 'Completed', 'Failed');

-- CreateEnum
CREATE TYPE "OnChainTransactionType" AS ENUM ('<PERSON>Batch', 'BurnBatch');

-- CreateTable
CREATE TABLE "OnchainTransaction" (
    "id" SERIAL NOT NULL,
    "chainId" INTEGER NOT NULL,
    "contractAddress" VARCHAR(255) NOT NULL,
    "txnHash" VARCHAR(255),
    "txnFee" BIGINT,
    "blockNumber" INTEGER,
    "blockTimestamp" INTEGER,
    "blockHash" VARCHAR(255),
    "fromAddress" VARCHAR(255),
    "toAddress" VARCHAR(255),
    "data" JSONB,
    "unsignedRaw" TEXT,
    "unsignedTxnId" VARCHAR(255),
    "signedRaw" TEXT,
    "transferAmount" BIGINT,
    "currency" VARCHAR(10),
    "method" VARCHAR(50),
    "type" "OnChainTransactionType" NOT NULL,
    "status" "OnChainTransactionStatus" NOT NULL,
    "errorMessage" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3),
    "referenceId" INTEGER NOT NULL,

    CONSTRAINT "OnchainTransaction_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "OnchainTransaction_txnHash_key" ON "OnchainTransaction"("txnHash");
