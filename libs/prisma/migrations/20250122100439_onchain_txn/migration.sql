/*
  Warnings:

  - The values [<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>] on the enum `OnChainTransactionType` will be removed. If these variants are still used in the database, this will fail.

*/
-- CreateEnum
CREATE TYPE "BattleOnchainStatus" AS ENUM ('Pending', 'InProgress', 'Completed');

-- AlterEnum
BEGIN;
CREATE TYPE "OnChainTransactionType_new" AS ENUM ('ResolveDebate');
ALTER TABLE "OnchainTransaction" ALTER COLUMN "type" TYPE "OnChainTransactionType_new" USING ("type"::text::"OnChainTransactionType_new");
ALTER TYPE "OnChainTransactionType" RENAME TO "OnChainTransactionType_old";
ALTER TYPE "OnChainTransactionType_new" RENAME TO "OnChainTransactionType";
DROP TYPE "OnChainTransactionType_old";
COMMIT;

-- AlterTable
ALTER TABLE "BATTLE_Battle" ADD COLUMN     "onchain_status" "BattleOnchainStatus" NOT NULL DEFAULT 'Pending';
