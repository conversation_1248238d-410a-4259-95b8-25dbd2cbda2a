// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl-openssl-3.0.x"]
}

generator json {
  provider = "prisma-json-types-generator"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        Int    @id @default(autoincrement()) @map("id")
  walletAddress    String @unique @map("wallet_address")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("BATTLE_User")
}

model Event {
  id                   String   @id @map("id")
  status               String   @default("New") @map("status")
  name                 String   @map("name")
  smartContractAddress String   @map("smartContractAddress")
  txHash               String   @unique @map("txHash")
  returnValues         Json?    @map("returnValues")
  blockNumber          Int      @map("blockNumber")
  timestamp            DateTime @map("timestamp")
  logIndex             Int      @map("logIndex")
  createdAt            DateTime @map("createdAt")
  updatedAt            DateTime @map("updatedAt")
  processErrorMsg      String?  @map("processErrorMsg")

  @@map("BATTLE_Event")
}

model Battle {
  id                          Int       @id @default(autoincrement()) @map("id")
  topic                       String    @map("topic")
  created_at                  DateTime  @map("created_at")
  updated_at                  DateTime  @map("updated_at")
  agent1_id                   Int       @map("agent1_id")
  agent2_id                   Int       @map("agent2_id")
  winner_id                   Int?      @map("winner_id")
  start_at                    DateTime  @map("start_at")
  duration                    Int       @map("duration")
  document_urls               Json?     @map("document_urls")
  content                     String?   @map("content")
  resolved_onchain_status     String    @default("Pending") @map("resolved_onchain_status")
  created_onchain_status      String    @default("Pending") @map("created_onchain_status")
  resolved_onchain_fail_count Int       @default(0) @map("resolved_onchain_fail_count")
  deleted_at                  DateTime? @map("deleted_at")
  platform_fee                Float?    @map("platform_fee")
  public_at                   DateTime? @map("public_at")
  markAsRefundedOnchainStatus String    @default("Pending") @map("mark_as_refunded_onchain_status")
  refundedOnchainStatus       String    @default("Pending") @map("refunded_onchain_status")
  status                      String    @map("status")

  @@map("BATTLE_Battle")
}

model BattleUserBet {
  id         Int      @id @default(autoincrement())
  betAmount  Decimal  @map("bet_amount") @db.Decimal(40, 20)
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")
  agentId    Int      @map("agent_id")
  battleId   Int      @map("battle_id")
  userWallet String   @map("user_wallet")
  txnHash    String?  @map("txn_hash")

  @@map("BATTLE_Battle_User_Bet")
}

model UserClaimHistory {
  id         Int      @id @default(autoincrement()) @map("id")
  battleId   Int      @map("battle_id")
  amount     Decimal  @map("amount") @db.Decimal(40, 20)
  userWallet String   @map("user_wallet")
  txnHash    String?  @map("txn_hash")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  @@map("BATTLE_User_Claim_History")
}

model OnchainTransaction {
  id              Int       @id @default(autoincrement()) @map("id")
  chainId         Int       @map("chain_id")
  contractAddress String    @map("contract_address") @db.VarChar(255)
  txnHash         String?   @unique @map("txn_hash") @db.VarChar(255)
  txnFee          BigInt?   @map("txn_fee")
  blockNumber     Int?      @map("block_number")
  blockTimestamp  Int?      @map("block_timestamp")
  blockHash       String?   @map("block_hash") @db.VarChar(255)
  fromAddress     String?   @map("from_address") @db.VarChar(255)
  toAddress       String?   @map("to_address") @db.VarChar(255)
  // Triple slash below is required for Json Type to work DO NOT REMOVE
  /// [OnChainTransactionData]
  data            Json?     @map("data")
  unsignedRaw     String?   @map("unsigned_raw") @db.VarChar(255)
  unsignedTxnId   String?   @map("unsigned_txn_id") @db.VarChar(255)
  signedRaw       String?   @map("signed_raw") @db.VarChar(255)
  transferAmount  BigInt?   @map("transfer_amount")
  currency        String?   @map("currency") @db.VarChar(10)
  method          String?   @map("method") @db.VarChar(50)
  type            String    @map("type")
  status          String    @default("InQueue") @map("status")
  errorMessage    String?   @map("error_message")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime? @updatedAt @map("updated_at")
  referenceId     Int       @map("reference_id")

  @@map("BATTLE_Onchain_Transaction")
}

model UserRefundHistory {
  id         Int      @id @default(autoincrement()) @map("id")
  battleId   Int      @map("battle_id")
  amount     Decimal  @map("amount") @db.Decimal(40, 20)
  userWallet String   @map("user_wallet")
  txnHash    String?  @map("txn_hash")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  @@map("BATTLE_User_Refunded_History")
}

enum OnChainTransactionStatus {
  InQueue
  Processing
  Sent
  Completed
  Failed
}

enum OnChainTransactionType {
  ResolveDebate
  MarkRefundable
}

enum BattleResolvedOnchainStatus {
  Pending
  InProgress
  Completed
  Failed
}

enum BattleCreatedOnchainStatus {
  Pending
  Successful
  Failed
}

enum EventType {
  DebateResolved
  BetPlaced
  UserClaimed
  DebateCreated
  DebateUpdated
  DebateDeleted
  DebateMarkedRefundable
  RefundSuccessful
  UserRefunded
}

enum EventStatus {
  New
  Confirmed
  Processing
  Completed
  Failed
}

enum BattleMarkAsRefundedStatus {
  Pending
  InProgress
  Completed
  Failed
}

enum BattleRefundedSuccessfulStatus {
  Pending
  InProgress
  Completed
  Failed
}

enum BattleStatus {
  Draft
  Active
  Cancelled
}
