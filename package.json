{"name": "@ai-debate/source", "version": "0.0.0", "license": "MIT", "scripts": {}, "private": true, "dependencies": {"@nestjs/common": "^10.0.2", "@nestjs/config": "^4.0.0", "@nestjs/core": "^10.0.2", "@nestjs/event-emitter": "^3.0.1", "@nestjs/platform-express": "^10.0.2", "@nestjs/schedule": "^4.1.0", "@nestjs/swagger": "^11.0.2", "@prisma/client": "^6.2.1", "axios": "^1.6.0", "ethers": "^6.13.5", "ioredis": "^5.5.0", "joi": "^17.13.3", "prisma": "^6.2.1", "prisma-json-types-generator": "^3.2.2", "redis": "^4.7.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0", "uuid": "^11.0.5"}, "prisma": {"schema": "libs/prisma/schema.prisma", "seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} libs/prisma/seeds/seed.ts"}, "devDependencies": {"@eslint/js": "^9.8.0", "@nestjs/schematics": "^10.0.1", "@nestjs/testing": "^10.0.2", "@nx/eslint": "20.3.1", "@nx/eslint-plugin": "20.3.1", "@nx/jest": "20.3.1", "@nx/js": "20.3.1", "@nx/nest": "20.3.1", "@nx/node": "20.3.1", "@nx/web": "20.3.1", "@nx/webpack": "20.3.1", "@nx/workspace": "20.3.1", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/jest": "^29.5.12", "@types/node": "~18.16.9", "eslint": "^9.8.0", "eslint-config-prettier": "^9.0.0", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "nx": "20.3.1", "prettier": "^2.6.2", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "~5.6.2", "typescript-eslint": "^8.13.0", "webpack-cli": "^5.1.4"}}