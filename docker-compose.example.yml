services: 
  worker: 
    build: 
      context: .
      dockerfile: ./apps/worker/Dockerfile
    container_name: ai-debate-worker
    restart: always
    # depends_on:
    #   - db-postgres
    #   - redis
    # networks:
    #   - ai-debate-network
    environment:
      DATABASE_URL:
    ports:
      - 3203:3000
  indexer:
    build: 
      context: ./apps/indexer
      dockerfile: Dockerfile
    container_name: ai-debate-indexer
    restart: always
    # depends_on:
    #   - db-postgres
    # networks:
    #   - ai-debate-network
    environment:
      DB_URL: 
