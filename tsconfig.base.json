{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@ai-debate/config": ["libs/config/src/index.ts"], "@ai-debate/prisma-client": ["libs/prisma-client/src/index.ts"], "@ai-debate/utils": ["libs/utils/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}