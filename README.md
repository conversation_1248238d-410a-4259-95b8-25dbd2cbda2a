# AiDebate
## Step by step to run project
- Except for `indexer` service, followed `readme` file in indexer folder
### 1. Set up enviroment:
- NodeJS `v18.20.4` is recommended
- Install `nx` if not already there:
    - `npm instlal -g nx`
### 2. Install dependencies 
- `yarn install`
### 3. Set up env variables:
- Set up variables like `.env.example` for all `.env` files.
### 4. Migrating database
- If you are fresh cloning this project, you need to also run `pnpm generate`
- If you are trying to run the server on your own database then run `pnpm migrate`
- If your local schema is out of sync with the dev/production database use `pnpm db:pull` to update it
### 5. Run services
- `nx serve [service-name]`

# Step by step to deploy apps (using Docker compose)

### 1. Install dependencies 
- Run `pnpm install` to install dependencies for common apps
- With **indexer** source, cd to **/apps/indexer/** then run `pnpm install`
### 2. Generate prisma 
- If your os platform of your machine is not `linux-musl`, add `binaryTargets = ["native", "linux-musl-openssl-3.0.x"]` (to be compatible with container's os platform) to object below at `schema.prisma` file:
    ```
    generator client {
        provider = "prisma-client-js"
        binaryTargets = ["native", "linux-musl-openssl-3.0.x"]
    }
    ```
- Run `pnpm generate`
### 3. Build apps
- Run `npx nx build ${app_folder_name}` to build app
- With **FE source**, need to add **.env** before build
- With **indexer** source, follow at **/apps/indexer/README.md**
### 4. Build image with docker compose
- Run `docker compose -f ${docker_compose_file} build `. Example: `docker compose -f docker-compose.staging.yml build`
### 5. Up containers with docker compose
- Run `docker compose -f ${docker_compose_file} up -d`. Example: `docker compose -f docker-compose.staging.yml up -d`

## Add new projects

While you could add new projects to your workspace manually, you might want to leverage [Nx plugins](https://nx.dev/concepts/nx-plugins?utm_source=nx_project&utm_medium=readme&utm_campaign=nx_projects) and their [code generation](https://nx.dev/features/generate-code?utm_source=nx_project&utm_medium=readme&utm_campaign=nx_projects) feature.

Use the plugin's generator to create new projects.

To generate a new application, use:

```sh
npx nx g @nx/nest:app demo
```

To generate a new library, use:

```sh
npx nx g @nx/node:lib mylib
```

You can use `npx nx list` to get a list of installed plugins. Then, run `npx nx list <plugin-name>` to learn about more specific capabilities of a particular plugin. Alternatively, [install Nx Console](https://nx.dev/getting-started/editor-setup?utm_source=nx_project&utm_medium=readme&utm_campaign=nx_projects) to browse plugins and generators in your IDE.

[Learn more about Nx plugins &raquo;](https://nx.dev/concepts/nx-plugins?utm_source=nx_project&utm_medium=readme&utm_campaign=nx_projects) | [Browse the plugin registry &raquo;](https://nx.dev/plugin-registry?utm_source=nx_project&utm_medium=readme&utm_campaign=nx_projects)
