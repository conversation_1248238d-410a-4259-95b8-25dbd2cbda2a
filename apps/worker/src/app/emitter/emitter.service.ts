import { PrismaService } from '@ai-debate/prisma-client';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import {
  Battle,
  BattleUserBet,
  EventType,
  UserClaimHistory,
} from '@prisma/client';
import Redis from 'ioredis';
import { RedisClientType } from 'redis';

@Injectable()
export class EmitterService {
  private readonly logger = new Logger(EmitterService.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly emitterService: EventEmitter2,
    @Inject('REDIS_SERVICE') private redisClient: RedisClientType
  ) {}

  @OnEvent(EventType.DebateCreated)
  private async OnchainDebateCreated(payload: Battle) {
    const data = {
      type: EventType.DebateCreated,
      payload: payload,
    };
    try {
      this.logger.log(
        `Received event: ${EventType.DebateCreated}, Payload: ${JSON.stringify(
          payload
        )}`
      );

      const publishResult = await this.redisClient.publish(
        EventType.DebateCreated,
        JSON.stringify(data)
      );

      this.logger.log(
        `Published event ${EventType.DebateCreated} to Redis, result: ${publishResult}`
      );
    } catch (error) {
      this.logger.error('Error handling DebateCreated event', error);
    }
  }

  @OnEvent(EventType.DebateDeleted)
  private async OnchainDebateDeleted(payload: Battle) {
    const data = {
      type: EventType.DebateDeleted,
      payload: payload,
    };

    try {
      this.logger.log(
        `Received event: ${EventType.DebateDeleted}, Payload: ${JSON.stringify(
          payload
        )}`
      );

      const publishResult = await this.redisClient.publish(
        EventType.DebateDeleted,
        JSON.stringify(data)
      );

      this.logger.log(
        `Published event ${EventType.DebateDeleted} to Redis, result: ${publishResult}`
      );
    } catch (error) {
      this.logger.error('Error handling DebateDeleted event', error);
    }
  }

  @OnEvent(EventType.DebateUpdated)
  private async OnchainDebateUpdated(payload: Battle) {
    const data = {
      type: EventType.DebateUpdated,
      payload: payload,
    };
    try {
      this.logger.log(
        `Received event: ${EventType.DebateUpdated}, Payload: ${JSON.stringify(
          payload
        )}`
      );

      const publishResult = await this.redisClient.publish(
        EventType.DebateUpdated,
        JSON.stringify(data)
      );

      this.logger.log(
        `Published event ${EventType.DebateUpdated} to Redis, result: ${publishResult}`
      );
    } catch (error) {
      this.logger.error('Error handling DebateUpdated event', error);
    }
  }

  @OnEvent(EventType.UserClaimed)
  private async OnchainUserClaimed(payload: UserClaimHistory) {
    const data = {
      type: EventType.UserClaimed,
      payload: payload,
    };
    try {
      this.logger.log(
        `Received event: ${EventType.UserClaimed}, Payload: ${JSON.stringify(
          payload
        )}`
      );

      const publishResult = await this.redisClient.publish(
        EventType.UserClaimed,
        JSON.stringify(data)
      );

      this.logger.log(
        `Published event ${EventType.UserClaimed} to Redis, result: ${publishResult}`
      );
    } catch (error) {
      this.logger.error('Error handling UserClaimed event', error);
    }
  }

  @OnEvent(EventType.DebateResolved)
  private async OnchainDebateResolved(payload: Battle) {
    const data = {
      type: EventType.DebateResolved,
      payload: payload,
    };
    try {
      this.logger.log(
        `Received event: ${EventType.DebateResolved}, Payload: ${JSON.stringify(
          payload
        )}`
      );

      const publishResult = await this.redisClient.publish(
        EventType.DebateResolved,
        JSON.stringify(data)
      );

      this.logger.log(
        `Published event ${EventType.DebateResolved} to Redis, result: ${publishResult}`
      );
    } catch (error) {
      this.logger.error('Error handling DebateResolved event', error);
    }
  }

  @OnEvent(EventType.BetPlaced)
  private async OnchainBetPlaced(payload: BattleUserBet) {
    const data = {
      type: EventType.BetPlaced,
      payload: payload,
    };
    try {
      this.logger.log(
        `Received event: ${EventType.BetPlaced}, Payload: ${JSON.stringify(
          payload
        )}`
      );

      const publishResult = await this.redisClient.publish(
        EventType.BetPlaced,
        JSON.stringify(data)
      );

      this.logger.log(
        `Published event ${EventType.BetPlaced} to Redis, result: ${publishResult}`
      );
    } catch (error) {
      this.logger.error('Error handling BetPlaced event', error);
    }
  }

  @OnEvent(EventType.DebateMarkedRefundable)
  private async OnchainDebateMarkedRefundable(payload: Battle) {
    const data = {
      type: EventType.DebateMarkedRefundable,
      payload: payload,
    };
    try {
      this.logger.log(
        `Received event: ${EventType.DebateMarkedRefundable}, Payload: ${JSON.stringify(
          payload
        )}`
      );

      const publishResult = await this.redisClient.publish(
        EventType.DebateMarkedRefundable,
        JSON.stringify(data)
      );

      this.logger.log(
        `Published event ${EventType.DebateMarkedRefundable} to Redis, result: ${publishResult}`
      );
    } catch (error) {
      this.logger.error('Error handling DebateMarkedRefundable event', error);
    }
  }

  @OnEvent(EventType.RefundSuccessful)
  private async OnchainRefundSuccessful(payload: Battle) {
    const data = {
      type: EventType.RefundSuccessful,
      payload: payload,
    };
    try {
      this.logger.log(
        `Received event: ${EventType.RefundSuccessful}, Payload: ${JSON.stringify(
          payload
        )}`
      );

      const publishResult = await this.redisClient.publish(
        EventType.RefundSuccessful,
        JSON.stringify(data)
      );

      this.logger.log(
        `Published event ${EventType.RefundSuccessful} to Redis, result: ${publishResult}`
      );
    } catch (error) {
      this.logger.error('Error handling RefundSuccessful event', error);
    }
  }
}
