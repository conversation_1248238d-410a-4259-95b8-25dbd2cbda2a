import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { EmitterService } from './emitter.service';
import { PrismaService } from '@ai-debate/prisma-client';
import Redis from 'ioredis';
import { createClient } from 'redis';

@Module({
  imports: [],
  controllers: [],
  providers: [
    PrismaService,
    EmitterService,
    {
      provide: 'REDIS_SERVICE',
      useFactory: async () => {
        const redisClient = createClient({
          socket: {
            host: process.env.REDIS_HOST,
            port: parseInt(process.env.REDIS_PORT),
          },
          database: parseInt(process.env.REDIS_DB_CACHE),
          // username: process.env.REDIS_USERNAME || undefined,
          password: process.env.REDIS_PASS || undefined,
        });

        Logger.log('Connecting to Redis...');
        await redisClient.connect();
        Logger.log('Redis connection established successfully');

        return redisClient;
      },
    },
  ],
})
export class EmitterModule {}
