import { Modu<PERSON> } from '@nestjs/common';
import { TransactionProcessor } from './transaction.processor';
import { PrismaService } from '@ai-debate/prisma-client';
import { TxnProcessorFactory } from './processor-impl/processor.factory';
import { ResolveDebateProcessor } from './processor-impl/resolve-debate.processor';
import { MarkAsRefundDebateProcessor } from './processor-impl/mark-as-refund-debate.processor';

@Module({
  imports: [],
  controllers: [],
  providers: [
    PrismaService,
    TransactionProcessor,
    TxnProcessorFactory,
    ResolveDebateProcessor,
    MarkAsRefundDebateProcessor,
  ],
})
export class TransactionProcessorModule {}
