import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { <PERSON>ron } from '@nestjs/schedule';
import {
  BattleCreatedOnchainStatus,
  BattleMarkAsRefundedStatus,
  BattleResolvedOnchainStatus,
  BattleStatus,
  OnchainTransaction,
  OnChainTransactionStatus,
  OnChainTransactionType,
  Prisma,
  PrismaClient,
} from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';
import { ResolveDebateData } from '@ai-debate/utils';
import { CHAIN_CONFIG_KEY, ChainConfig } from '@ai-debate/config';
import { PrismaService } from '@ai-debate/prisma-client';
import { DefaultArgs } from '@prisma/client/runtime/library';
import { TransactionFactory } from './transactions/transaction.factory';
import { TxnProcessorFactory } from './processor-impl/processor.factory';
import { DebateMarkAsRefundedEventData } from '../event-processors/events/debate-mark-as-refunded.event';

@Injectable()
export class TransactionProcessor {
  private readonly logger = new Logger(TransactionProcessor.name);
  private pickExpiredDebateToResolveRunning = false;
  private pickDebateNeedMarkRefundRunning = false;
  private initialized = false;

  constructor(
    private configService: ConfigService,
    private prismaService: PrismaService,
    private txnProcessorFactory: TxnProcessorFactory
  ) {}

  @Cron('*/2 * * * * *')
  public async execute() {
    const runId = uuidv4();

    if (!this.initialized) {
      this.cleanUpEvents();
      this.initialized = true;
    }

    this.logger.debug(`[${runId}]: Transaction processing triggered`);
    try {
      // If any txns in processing then skip
      const processingTxnsCount =
        await this.prismaService.onchainTransaction.count({
          where: {
            status: OnChainTransactionStatus.Processing,
          },
        });

      if (processingTxnsCount > 0) {
        this.logger.debug(
          `[${runId}]: Skipping processing as there are ${processingTxnsCount} transactions in processing status`
        );
        return;
      }

      const updatedRows = await this.prismaService.$transaction(
        async ($transaction) => {
          const recordsToUpdate =
            await $transaction.onchainTransaction.findMany({
              where: { status: OnChainTransactionStatus.InQueue },
              take: 10,
              select: { id: true },
            });
          const idsToUpdate = recordsToUpdate.map((record) => record.id);
          return $transaction.onchainTransaction.updateMany({
            where: { id: { in: idsToUpdate } },
            data: { status: OnChainTransactionStatus.Processing },
          });
        }
      );

      const updatedRowsCount = updatedRows?.count || 0;
      if (+updatedRowsCount < 1) {
        this.logger.debug(
          `[${runId}]: There are no new transactions to process. Skipping processing until next trigger.`
        );
        return;
      }

      const txns = await this.prismaService.onchainTransaction.findMany({
        where: { status: OnChainTransactionStatus.Processing },
        orderBy: [{ createdAt: 'asc' }],
      });

      this.logger.debug(
        `[${runId}]: About to process newTransactions='${txns
          .map((txn) => txn.id)
          .join(',')}'`
      );

      for (const txn of txns) {
        try {
          await this.prismaService.$transaction(
            async ($transaction) => {
              await this.processTransaction({
                onchainTxn: txn,
                transaction: $transaction,
              });
            },
            {
              maxWait: 10000,
              timeout: 40000,
            }
          );

          this.logger.debug(
            `[${runId}]: Processed transaction ${txn.id} successfully.`
          );

        } catch (err) {
            this.logger.error(
              `[${runId}]: Processing transaction ${txn.id} failed: ${err.message}`
            );

            await this.prismaService.onchainTransaction.update({
              where: { id: txn.id },
              data: {
                errorMessage: `Error while parsing transaction err='${err.message}`,
                status: OnChainTransactionStatus.Failed,
                updatedAt: new Date(),
              },
            });

            await this.prismaService.battle.update({
              where: { id: txn.referenceId },
              data: {
                resolved_onchain_status: BattleResolvedOnchainStatus.Failed,
                resolved_onchain_fail_count: {
                  increment: 1,
                },
                updated_at: new Date(),
              },
            });
        }

        await this.delay(await this.getBlockTime());
      }
    } catch (error) {
      this.logger.error(
        `[${runId}]: Transaction processing error: ${error.message}`,
        error.stack
      );
      throw error;
      // await this.prismaService.onchainTransaction.updateMany({
      //   where: { status: OnChainTransactionStatus.Processing },
      //   data: {
      //     status: OnChainTransactionStatus.InQueue,
      //     updatedAt: new Date(),
      //   },
      // });
    }
  }

  private async processTransaction(params: {
    onchainTxn: OnchainTransaction;
    transaction: Omit<
      PrismaClient<Prisma.PrismaClientOptions, never, DefaultArgs>,
      '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
    >;
  }) {
    const { onchainTxn, transaction } = params;
    try {
      const baseTxn = TransactionFactory[onchainTxn.type](onchainTxn);
      const processor = this.txnProcessorFactory.get[onchainTxn.type];

      return await processor.execute(baseTxn, transaction);
    } catch (err) {
      await transaction.onchainTransaction.update({
        where: { id: onchainTxn.id },
        data: {
          errorMessage: `Error while parsing transaction err='${err.message}`,
          status: OnChainTransactionStatus.Failed,
          updatedAt: new Date(),
        },
      });
      await transaction.battle.update({
        where: { id: onchainTxn.referenceId },
        data: {
          resolved_onchain_status: BattleResolvedOnchainStatus.Failed,
          resolved_onchain_fail_count: {
            increment: 1,
          },
          updated_at: new Date(),
        },
      });
    }
  }

  @Cron('*/2 * * * * *')
  private async pickExpiredDebateToResolve() {
    if (this.pickExpiredDebateToResolveRunning) {
      return;
    }
    this.pickExpiredDebateToResolveRunning = true;

    try {
      const runId = uuidv4();

      const pendingDebates = await this.prismaService.battle.findMany({
        where: {
          OR: [
            {
              AND: [
                { resolved_onchain_status: BattleResolvedOnchainStatus.Pending },
                { winner_id: { not: null } },
                { created_onchain_status: BattleCreatedOnchainStatus.Successful },
              ],
            },
            {
              AND: [
                { resolved_onchain_status: BattleResolvedOnchainStatus.Failed },
                { resolved_onchain_fail_count: { lt: 3 } },
                { winner_id: { not: null } },
                { created_onchain_status: BattleCreatedOnchainStatus.Successful },
              ],
            },
          ],
        },
      });


      this.logger.debug(
        `[${runId}]: About to process pending debate = ${pendingDebates
          .map((item) => item.id)
          .join(',')}`
      );

      const expiredDebates = pendingDebates.filter((debate) => {
        const expireTimeMils =
          debate.start_at.getTime() + debate.duration * 60 * 1000;
        return expireTimeMils <= Date.now();
      });

      if (expiredDebates.length < 1) {
        this.logger.debug(`[${runId}]: No expired debated found`);
        return;
      }

      this.logger.debug(
        `[${runId}]: About to process expiredDebatesIds = ${expiredDebates
          .map((item) => item.id)
          .join(',')}`
      );

      const chainConfig =
        this.configService.getOrThrow<ChainConfig>(CHAIN_CONFIG_KEY);

      const onchainTxns = expiredDebates.map((debate) => {
        const resolvedDebateData: ResolveDebateData = {
          debateId: debate.id,
          winnerId: debate.winner_id,
        };
        const onchainTxn: Prisma.OnchainTransactionCreateInput = {
          type: OnChainTransactionType.ResolveDebate,
          status: OnChainTransactionStatus.InQueue,
          chainId: chainConfig.chainId,
          contractAddress: chainConfig.debateBettingContractAddress,
          referenceId: debate.id,
          data: resolvedDebateData,
        };
        return onchainTxn;
      });

      await this.prismaService.$transaction(async (txn) => {
        await txn.onchainTransaction.createMany({
          data: onchainTxns,
        });

        await txn.battle.updateMany({
          where: {
            id: {
              in: expiredDebates.map((item) => item.id),
            },
          },
          data: {
            resolved_onchain_status: BattleResolvedOnchainStatus.InProgress,
          },
        });
      });
    } catch (error) {
      this.logger.error(
        `pickExpiredDebateToResolve error: ${error.message}`,
        error.stack
      );
    } finally {
      this.pickExpiredDebateToResolveRunning = false;
    }
  }
  
  @Cron('*/2 * * * * *')
  private async pickDebateNeedMarkRefundd(){
    if (this.pickDebateNeedMarkRefundRunning) {
      return;
    }
    this.pickDebateNeedMarkRefundRunning = true;

    try {
      const runId = uuidv4();

      const debatesNeedMarkRefund = await this.prismaService.battle.findMany({
        where: {
          AND: [
            { markAsRefundedOnchainStatus: BattleMarkAsRefundedStatus.Pending },
            { created_onchain_status: BattleCreatedOnchainStatus.Successful },
            { status: BattleStatus.Cancelled},
          ],
        },
      });

      if (debatesNeedMarkRefund.length < 1) {
        this.logger.debug(`[${runId}]: No debates need to be marked as refundable`);
        return;
      }

      this.logger.debug(
        `[${runId}]: About to process debatesNeedMarkRefund = ${debatesNeedMarkRefund
          .map((item) => item.id)
          .join(',')}`
      );

      const chainConfig =
        this.configService.getOrThrow<ChainConfig>(CHAIN_CONFIG_KEY);

      const onchainTxns = debatesNeedMarkRefund.map((debate) => {
        const markRefundableData: DebateMarkAsRefundedEventData = {
          debateId: debate.id,
        };
        const onchainTxn: Prisma.OnchainTransactionCreateInput = {
          type: OnChainTransactionType.MarkRefundable,
          status: OnChainTransactionStatus.InQueue,
          chainId: chainConfig.chainId,
          contractAddress: chainConfig.debateBettingContractAddress,
          referenceId: debate.id,
          data: markRefundableData,
        };
        return onchainTxn;
      });

      await this.prismaService.$transaction(async (txn) => {
        await txn.onchainTransaction.createMany({
          data: onchainTxns,
        });

        await txn.battle.updateMany({
          where: {
            id: {
              in: debatesNeedMarkRefund.map((item) => item.id),
            },
          },
          data: {
            markAsRefundedOnchainStatus: BattleMarkAsRefundedStatus.InProgress,
          },
        });
      });
    } catch (error) {
      this.logger.error(
        `pickDebateNeedMarkRefund error: ${error.message}`,
        error.stack
      );
    } finally {
      this.pickDebateNeedMarkRefundRunning = false;
    }
  }

  private async cleanUpEvents() {
    this.logger.debug(
      `Initial transaction processor start. Cleaning up transactions that were processing before the app was restarted`
    );

    const updatedRows = await this.prismaService.onchainTransaction.updateMany({
      where: { status: OnChainTransactionStatus.Processing },
      data: { status: OnChainTransactionStatus.Failed, updatedAt: new Date() },
    });

    const updatedRowsCount = updatedRows?.count || 0;

    if (updatedRowsCount > 0) {
      this.logger.warn(
        `Cleaning up result: set ${updatedRowsCount} transactions to Failed status`
      );
    }

    this.logger.debug(`Cleaning up transactions finished`);
  }

  private async delay(t) {
    return new Promise((resolve) => setTimeout(resolve, t));
  }

  private async getBlockTime() {
    return this.configService.get<number>('chain.blockTimeInMilliseconds');
  }
}
