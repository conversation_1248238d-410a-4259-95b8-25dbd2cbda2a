import { OnchainTransaction, OnChainTransactionType } from '@prisma/client';
import { BaseTransaction } from './base.transaction';
import { ResolveDebateData } from '@ai-debate/utils';

export class ResolveDebateTransaction extends BaseTransaction {
  data: ResolveDebateData;
  constructor(txn: OnchainTransaction) {
    super({
      dbTransaction: txn,
      transactionType: OnChainTransactionType.ResolveDebate,
    });

    this.data = txn.data as ResolveDebateData;
  }
}
