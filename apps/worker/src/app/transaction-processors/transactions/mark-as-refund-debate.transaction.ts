import { MarkAsRefundDebateData } from '@ai-debate/utils';
import { OnchainTransaction, OnChainTransactionType } from '@prisma/client';
import { BaseTransaction } from './base.transaction';

export class MarkAsRefundDebateTransaction extends BaseTransaction {
  data: MarkAsRefundDebateData;
  constructor(txn: OnchainTransaction) {
    super({
      dbTransaction: txn,
      transactionType: OnChainTransactionType.MarkRefundable,
    });

    this.data = txn.data as MarkAsRefundDebateData;
  }
}
