import { OnchainTransaction, OnChainTransactionType } from '@prisma/client';

export abstract class BaseTransaction {
  transactionType: OnChainTransactionType;
  dbTransaction: OnchainTransaction;
  contractAddress: string;
  createdAt: Date;

  protected constructor(params: {
    transactionType: OnChainTransactionType;
    dbTransaction: OnchainTransaction;
  }) {
    const { transactionType, dbTransaction } = params;
    this.transactionType = transactionType;
    this.dbTransaction = dbTransaction;
    this.contractAddress = dbTransaction.contractAddress;
    this.createdAt = dbTransaction.createdAt;
  }
}
