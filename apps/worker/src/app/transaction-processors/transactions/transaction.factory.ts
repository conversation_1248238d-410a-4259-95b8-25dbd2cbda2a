import { OnChainTransactionType, OnchainTransaction } from '@prisma/client';
import { BaseTransaction } from './base.transaction';
import { ResolveDebateTransaction } from './resolve-debate.transaction';
import { MarkAsRefundDebateTransaction } from './mark-as-refund-debate.transaction';

export type TransactionMapper = {
  readonly [transactionType in OnChainTransactionType]: (
    txn: OnchainTransaction
  ) => BaseTransaction;
};

export const TransactionFactory: TransactionMapper = {
  [OnChainTransactionType.ResolveDebate]: (txn) => {
    return new ResolveDebateTransaction(txn);
  },
  [OnChainTransactionType.MarkRefundable]: (txn) => {
    return new MarkAsRefundDebateTransaction(txn);
  },
};
