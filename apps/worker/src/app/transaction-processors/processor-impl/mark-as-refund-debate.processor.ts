import { CHAIN_CONFIG_KEY, ChainConfig } from '@ai-debate/config';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { OnChainTransactionStatus, Prisma, PrismaClient } from '@prisma/client';
import { DefaultArgs } from '@prisma/client/runtime/library';
import { JsonFragment } from 'ethers';
import DebateBettingContract from '../abi/DebateBettingContract.json';
import { DebateBettingContractMethods } from '../const/transaction.const';
import { MarkAsRefundDebateTransaction } from '../transactions/mark-as-refund-debate.transaction';
import { BaseTransactionProcessor } from './base-transaction.processor';

@Injectable()
export class MarkAsRefundDebateProcessor extends BaseTransactionProcessor<MarkAsRefundDebateTransaction> {
  protected logger = new Logger(MarkAsRefundDebateProcessor.name);

  constructor(private readonly configService: ConfigService) {
    super();
  }

  protected getContractABI(): JsonFragment[] {
    return DebateBettingContract.abi;
  }

  protected getWalletPrivateKey(): string {
    return this.configService.getOrThrow<ChainConfig>(CHAIN_CONFIG_KEY)
      .operatorPrivateKey;
  }

  protected getProviderUrl(): string {
    return this.configService.getOrThrow<ChainConfig>(CHAIN_CONFIG_KEY)
      .rpcEndpoint;
  }

  protected async prepare(
    transaction: MarkAsRefundDebateTransaction,
    prismaTransaction: Omit<
      PrismaClient<Prisma.PrismaClientOptions, never, DefaultArgs>,
      '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
    >
  ): Promise<void> {
    try {
      this.logger.debug(
        `MarkAsRefundDebateProcessor::prepare transaction=${JSON.stringify(
          transaction
        )}`
      );

      const markAsRefundDebateRequest = {
        chainId: transaction.dbTransaction.chainId,
        contractAddress: transaction.contractAddress,
        method: DebateBettingContractMethods.MarkAsRefundDebate,
        args: [transaction.data.debateId],
      };

      this.logger.debug(
        `MarkAsRefundDebateProcessor::prepare markAsRefundDebateRequest=${JSON.stringify(
          markAsRefundDebateRequest
        )}`
      );

      const txnResponse = await this.sendTransaction(markAsRefundDebateRequest);

      await prismaTransaction.onchainTransaction.update({
        where: {
          id: transaction.dbTransaction.id,
        },
        data: {
          status: OnChainTransactionStatus.Sent,
          txnHash: txnResponse.hash,
          fromAddress: txnResponse.from,
          toAddress: txnResponse.to,
          unsignedRaw: txnResponse.data,
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      this.logger.error(
        `MarkAsRefundDebateProcessor::prepare error: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }
}
