import { Logger } from '@nestjs/common';
import { BaseTransaction } from '../transactions/base.transaction';
import { ethers, JsonFragment, TransactionResponse } from 'ethers';
import { Prisma, PrismaClient } from '@prisma/client';
import { DefaultArgs } from '@prisma/client/runtime/library';
import { waitForExecutionOrTimeout } from '../const/transaction.const';

export abstract class BaseTransactionProcessor<T extends BaseTransaction> {
  protected readonly logger: Logger = new Logger(BaseTransactionProcessor.name);

  protected async sendTransaction(params: {
    chainId: number;
    contractAddress: string;
    method: string;
    args: any[];
  }): Promise<TransactionResponse> {
    const { chainId, contractAddress, method, args } = params;
    const provider = this.getJsonRpcProvider();
    const signer = this.getSigner();
    const contract = this.getContract(contractAddress);

    const nonce = await signer.getNonce();
    this.logger.debug('Nonce', nonce);

    // Step 2: Fetch the gas price
    const feeData = await provider.getFeeData();
    this.logger.debug('Gas Price: ', feeData.gasPrice.toString());

    // Step 3: Encode the transaction data (call to SC function)
    const txData = contract.interface.encodeFunctionData(method, args);
    this.logger.debug('Transaction Data: ', txData);

    // Step 4: Estime the gas limit for the SC function
    const gasLimit = await signer.estimateGas({
      to: contractAddress,
      data: txData,
    });
    this.logger.debug('Gas Limit: ', gasLimit.toString());

    // Step 5: Build the raw transaction
    const txRaw = {
      to: contractAddress,
      value: 0,
      nonce,
      gasLimit,
      gasPrice: feeData.gasPrice * 2n,
      data: txData,
      chainId,
    };

    // Step 6: Sign the transaction offline
    const signedTx = await signer.signTransaction(txRaw);
    this.logger.debug('Signed Transaction: ', signedTx);

    // Step 7: Send the signed transaction
    const txResponse = await provider.broadcastTransaction(signedTx);
    this.logger.debug('Transaction Hash: ', txResponse.hash);
    return txResponse;
  }

  public async execute(
    transaction: T,
    t: Omit<
      PrismaClient<Prisma.PrismaClientOptions, never, DefaultArgs>,
      '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
    >
  ) {
    try {
      await waitForExecutionOrTimeout(this.prepare.bind(this, transaction, t));
    } catch (error) {
      throw new Error(`Error while executing transaction: ${error}`);
    }
  }

  protected abstract prepare(
    transaction: T,

    _: Omit<
      PrismaClient<Prisma.PrismaClientOptions, never, DefaultArgs>,
      '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
    >
  ): Promise<void>;

  protected getSigner() {
    const walletKey = this.getWalletPrivateKey();
    return new ethers.Wallet(walletKey, this.getJsonRpcProvider());
  }

  protected getJsonRpcProvider() {
    return new ethers.JsonRpcProvider(this.getProviderUrl());
  }

  protected getContract(address: string) {
    return new ethers.Contract(
      address,
      this.getContractABI(),
      this.getSigner()
    );
  }

  protected abstract getProviderUrl(): string;

  protected abstract getWalletPrivateKey(): string;

  protected abstract getContractABI(): JsonFragment[];
}
