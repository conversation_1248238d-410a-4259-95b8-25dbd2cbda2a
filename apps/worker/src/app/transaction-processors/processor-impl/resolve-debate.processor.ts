import { Injectable, Logger } from '@nestjs/common';
import { ResolveDebateTransaction } from '../transactions/resolve-debate.transaction';
import { BaseTransactionProcessor } from './base-transaction.processor';
import { ConfigService } from '@nestjs/config';
import { JsonFragment } from 'ethers';
import { CHAIN_CONFIG_KEY, ChainConfig } from '@ai-debate/config';
import { PrismaClient, Prisma, OnChainTransactionStatus } from '@prisma/client';
import { DefaultArgs } from '@prisma/client/runtime/library';
import { DebateBettingContractMethods } from '../const/transaction.const';
import DebateBettingContract from '../abi/DebateBettingContract.json';

@Injectable()
export class ResolveDebateProcessor extends BaseTransactionProcessor<ResolveDebateTransaction> {
  protected logger = new Logger(ResolveDebateProcessor.name);

  constructor(private readonly configService: ConfigService) {
    super();
  }

  protected getContractABI(): JsonFragment[] {
    return DebateBettingContract.abi;
  }

  protected getWalletPrivateKey(): string {
    return this.configService.getOrThrow<ChainConfig>(CHAIN_CONFIG_KEY)
      .operatorPrivateKey;
  }

  protected getProviderUrl(): string {
    return this.configService.getOrThrow<ChainConfig>(CHAIN_CONFIG_KEY)
      .rpcEndpoint;
  }

  protected async prepare(
    transaction: ResolveDebateTransaction,
    prismaTransaction: Omit<
      PrismaClient<Prisma.PrismaClientOptions, never, DefaultArgs>,
      '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
    >
  ): Promise<void> {
    try {
      this.logger.debug(
        `ResolveDebateProcessor::prepare transaction=${JSON.stringify(
          transaction
        )}`
      );

      const resolveDebateRequest = {
        chainId: transaction.dbTransaction.chainId,
        contractAddress: transaction.contractAddress,
        method: DebateBettingContractMethods.ResolveDebate,
        args: [transaction.data.debateId, transaction.data.winnerId],
      };

      this.logger.debug(
        `ResolveDebateProcessor::prepare resolveDebateRequest=${JSON.stringify(
          resolveDebateRequest
        )}`
      );

      const txnResponse = await this.sendTransaction(resolveDebateRequest);

      await prismaTransaction.onchainTransaction.update({
        where: {
          id: transaction.dbTransaction.id,
        },
        data: {
          status: OnChainTransactionStatus.Sent,
          txnHash: txnResponse.hash,
          fromAddress: txnResponse.from,
          toAddress: txnResponse.to,
          unsignedRaw: txnResponse.data,
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      this.logger.error(
        `ResolveDebateProcessor::prepare error: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }
}
