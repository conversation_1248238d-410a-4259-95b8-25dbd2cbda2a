import { Injectable } from '@nestjs/common';
import { ResolveDebateProcessor } from './resolve-debate.processor';
import { BaseTransactionProcessor } from './base-transaction.processor';
import { OnChainTransactionType } from '@prisma/client';
import { BaseTransaction } from '../transactions/base.transaction';
import { MarkAsRefundDebateProcessor } from './mark-as-refund-debate.processor';

export type TransactionProcessorMapper = {
  readonly [transactionType in OnChainTransactionType]: BaseTransactionProcessor<BaseTransaction>;
};

@Injectable()
export class TxnProcessorFactory {
  constructor(
    private readonly resolveDebateProcessor: ResolveDebateProcessor,
    private readonly markAsRefundDebateProcessor: MarkAsRefundDebateProcessor
  ) {}

  public get: TransactionProcessorMapper = {
    [OnChainTransactionType.ResolveDebate]: this.resolveDebateProcessor,
    [OnChainTransactionType.MarkRefundable]: this.markAsRefundDebateProcessor,
  };
}
