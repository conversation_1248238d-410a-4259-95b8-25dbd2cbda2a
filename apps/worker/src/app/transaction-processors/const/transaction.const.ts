export const DebateBettingContractMethods = {
  ResolveDebate: 'adminResolveDebate',
  MarkAsRefundDebate: 'adminMarkDebateRefundable',
};

export const getTimeoutInMs = () =>
  parseInt(process.env.EVENT_HANDLING_TIMEOUT_IN_MS ?? '20000');

export const waitForExecutionOrTimeout = async (
  task: () => Promise<void>
): Promise<void> => {
  let timer: NodeJS.Timer;
  return await Promise.race<void>([
    new Promise((_, reject) => {
      const timeoutInMs = getTimeoutInMs();
      timer = setTimeout(() => reject(new TimeoutError()), timeoutInMs);
    }),
    task(),
  ]).finally(() => clearTimeout(timer));
};

export class TimeoutError extends Error {
  constructor() {
    super('Timeout occurred');
  }
}
