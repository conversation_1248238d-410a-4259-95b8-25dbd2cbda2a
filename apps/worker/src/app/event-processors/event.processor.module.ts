import { PrismaService } from '@ai-debate/prisma-client';
import { EventProcessorFactory } from './processors/processor.factory';
import { Module } from '@nestjs/common';
import { MainEventProcessor } from './main.processor';
import { DebateResolvedEventProcessor } from './processors/debate-resolved.processor';
import { BetPlacedEventProcessor } from './processors/bet-placed.processor';
import { UserClaimedEventProcessor } from './processors/user-claimed.processor';
import { DebateCreatedEventProcessor } from './processors/debate-created.processor';
import { DebateUpdatedEventProcessor } from './processors/debate-updated.processor';
import { DebateDeletedEventProcessor } from './processors/debate-deleted.processor';
import { DebateMarkAsRefundedEventProcessor } from './processors/debate-mark-as-refunded.processor';
import { DebateRefundSucessfulEventProcessor } from './processors/debate-refundsuccessful.processor';
import { UserRefundedEventProcessor } from './processors/user-refunded.processor';

@Module({
  imports: [],
  controllers: [],
  providers: [
    PrismaService,
    EventProcessorFactory,
    MainEventProcessor,
    DebateResolvedEventProcessor,
    BetPlacedEventProcessor,
    UserClaimedEventProcessor,
    DebateCreatedEventProcessor,
    DebateUpdatedEventProcessor,
    DebateDeletedEventProcessor,
    DebateMarkAsRefundedEventProcessor,
    DebateRefundSucessfulEventProcessor,
    UserRefundedEventProcessor,
  ],
})
export class EventProcessorModule {}
