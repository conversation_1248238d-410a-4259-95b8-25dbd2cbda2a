import { PrismaService } from '@ai-debate/prisma-client';
import { Injectable, Logger } from '@nestjs/common';
import { EventProcessorFactory } from './processors/processor.factory';
import { Cron } from '@nestjs/schedule';
import { v4 as uuidv4 } from 'uuid';
import { Event, EventStatus, Prisma, PrismaClient } from '@prisma/client';
import { DefaultArgs } from '@prisma/client/runtime/library';
import { EventFactory } from './events/event.factory';

@Injectable()
export class MainEventProcessor {
  private readonly logger = new Logger(MainEventProcessor.name);
  private initialized = false;

  constructor(
    private prismaService: PrismaService,
    private eventProcessorFactory: EventProcessorFactory
  ) {}

  @Cron('*/5 * * * * *')
  public async execute() {
    const runId = uuidv4();

    if (!this.initialized) {
      await this.cleanUpEvents();
      this.initialized = true;
    }

    this.logger.debug(`[${runId}]: Event processing triggered`);

    try {
      const processingEvent = await this.prismaService.event.count({
        where: { status: EventStatus.Processing },
      });

      if (+processingEvent > 0) {
        this.logger.warn(
          `[${runId}]: Some events are still being processed. Skipping processing until next trigger.`
        );
        return;
      }

      const updatedRows = await this.prismaService.event.updateMany({
        where: { status: EventStatus.Confirmed },
        data: { status: EventStatus.Processing },
      });

      const updatedRowsCount = updatedRows?.count || 0;

      if (+updatedRowsCount < 1) {
        this.logger.debug(
          `[${runId}]: There are no new events to process. Skipping processing until next trigger.`
        );
        return;
      }

      const events = await this.prismaService.event.findMany({
        where: { status: EventStatus.Processing },
        orderBy: [{ blockNumber: 'asc' }, { logIndex: 'asc' }],
      });

      this.logger.debug(
        `[${runId}]: About to process newEvents='${events.length}'`
      );

      await this.prismaService.$transaction(async (transaction) => {
        for (let i = 0; i < events.length; i++) {
          await this.processEvent(events[i], transaction);
        }
      }, {
        timeout: 40000,
        maxWait: 10000,
      });

      this.logger.debug(
        `[${runId}]: Finished processing newEvents='${events.length}'`
      );
    } catch (e) {
      this.logger.error(`MainEventProcessor::execute error='${e.message}'`, e);
      throw e;
    }
  }

  private async cleanUpEvents() {
    this.logger.debug(
      `Initial event processor start. Cleaning up events that were processing before the app was restarted`
    );

    const updatedRows = await this.prismaService.event.updateMany({
      where: { status: EventStatus.Processing },
      data: { status: EventStatus.Failed, updatedAt: new Date() },
    });

    const updatedRowsCount = updatedRows?.count || 0;

    if (updatedRowsCount > 0) {
      this.logger.warn(
        `Cleaning up result: set ${updatedRowsCount} events to ERROR`
      );
    }

    this.logger.debug(`Cleaning up events finished`);
  }

  private async processEvent(
    event: Event,
    transaction: Omit<
      PrismaClient<Prisma.PrismaClientOptions, never, DefaultArgs>,
      '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
    >
  ) {
    try {
      const baseEvent = EventFactory[event.name](event);
      const processor = this.eventProcessorFactory.get[event.name];

      if (!processor) {
        this.prismaService.event.update({
          where: { id: event.id },
          data: {
            status: EventStatus.Failed,
            updatedAt: new Date(),
            processErrorMsg: `no processor found for eventName='${event.name}'`,
          },
        });
        return;
      }

      return await processor.execute(baseEvent, transaction);
    } catch (e) {
      this.logger.error(
        `eventName='${event.name}' (txHash='${event.txHash}') error while parsing event err='${e.message}'`,
        e
      );
    }
  }
}
