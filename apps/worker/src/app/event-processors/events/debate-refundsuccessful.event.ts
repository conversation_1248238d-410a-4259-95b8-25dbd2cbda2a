import { Event, EventType } from '@prisma/client';
import { BaseEvent } from './base.event';

export interface DebateRefundSuccessfulEventData {
  debateId: number;
  amountOfUsers: number;
}

export class DebateRefundSuccessfulEvent extends BaseEvent {
  data: DebateRefundSuccessfulEventData;

  constructor(dbEvent: Event) {
    super(dbEvent, EventType.RefundSuccessful);
    this.data = dbEvent.returnValues as DebateRefundSuccessfulEventData;
  }
}
