import { Event, EventType } from '@prisma/client';
import { BaseEvent } from './base.event';
import { Decimal } from '@prisma/client/runtime/library';

export interface BetPlacedEventData {
  bettor: string;
  debateId: number;
  chosenAgentId: number;
  amount: Decimal;
}

export class BetPlacedEvent extends BaseEvent {
  data: BetPlacedEventData;

  constructor(dbEvent: Event) {
    super(dbEvent, EventType.BetPlaced);
    this.data = dbEvent.returnValues as BetPlacedEventData;
  }
}
