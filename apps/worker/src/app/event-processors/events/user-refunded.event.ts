import { Event, EventType } from '@prisma/client';
import { BaseEvent } from './base.event';

export interface UserRefundedEventData {
  bettor: string;
  amount: number;
  debateId: number;
  txHash: string;
}

export class UserRefundedEvent extends BaseEvent {
  data: UserRefundedEventData;

  constructor(dbEvent: Event) {
    super(dbEvent, EventType.UserRefunded);
    this.data = dbEvent.returnValues as UserRefundedEventData;
    this.data.txHash = dbEvent.txHash;
  }
}
