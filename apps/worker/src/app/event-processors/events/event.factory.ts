import { Event, EventType } from '@prisma/client';
import { BaseEvent } from './base.event';
import { BetPlacedEvent } from './bet-placed.event';
import { DebateCreatedEvent } from './debate-created.event';
import { DebateDeletedEvent } from './debate-deleted.event';
import { DebateMarkAsRefundedEvent } from './debate-mark-as-refunded.event';
import { DebateResolvedEvent } from './debate-resolved.event';
import { DebateUpdatedEvent } from './debate-updated.event';
import { UserClaimedEvent } from './user-claimed.event';
import { DebateRefundSuccessfulEvent } from './debate-refundsuccessful.event';
import { UserRefundedEvent } from './user-refunded.event';

export type EventMapper = {
  readonly [eventType in EventType]: (event: Event) => BaseEvent;
};

export const EventFactory: EventMapper = {
  [EventType.DebateResolved]: (event: Event) => new DebateResolvedEvent(event),
  [EventType.BetPlaced]: (event: Event) => new BetPlacedEvent(event),
  [EventType.UserClaimed]: (event: Event) => new UserClaimedEvent(event),
  [EventType.DebateCreated]: (event: Event) => new DebateCreatedEvent(event),
  [EventType.DebateUpdated]: (event: Event) => new DebateUpdatedEvent(event),
  [EventType.DebateDeleted]: (event: Event) => new DebateDeletedEvent(event),
  [EventType.DebateMarkedRefundable]: (event: Event) => new DebateMarkAsRefundedEvent(event),
  [EventType.RefundSuccessful]: (event: Event) => new DebateRefundSuccessfulEvent(event),
  [EventType.UserRefunded]: (event: Event) => new UserRefundedEvent(event),
};
