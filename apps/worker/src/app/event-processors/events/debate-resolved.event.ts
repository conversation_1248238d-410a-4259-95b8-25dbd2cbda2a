import { Event, EventType } from '@prisma/client';
import { BaseEvent } from './base.event';

export interface DebateResolvedEventData {
  debateId: number;
  winAgentId: number;
}

export class DebateResolvedEvent extends BaseEvent {
  data: DebateResolvedEventData;

  constructor(dbEvent: Event) {
    super(dbEvent, EventType.DebateResolved);
    this.data = dbEvent.returnValues as DebateResolvedEventData;
  }
}
