import { Event, EventType } from '@prisma/client';
import { BaseEvent } from './base.event';

export interface DebateUpdatedEventData {
  debateId: number;
  agentAID: number;
  agentBID: number;
  platformFeePercentage: number;
  publicTimeStamp: number;
  startTimeStamp: number;
  sessionDuration: number;
}

export class DebateUpdatedEvent extends BaseEvent {
  data: DebateUpdatedEventData;

  constructor(dbEvent: Event) {
    super(dbEvent, EventType.DebateUpdated);
    this.data = dbEvent.returnValues as DebateUpdatedEventData;
  }
}
