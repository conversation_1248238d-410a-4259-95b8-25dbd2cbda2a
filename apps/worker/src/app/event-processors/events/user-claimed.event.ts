import { Event, EventType } from '@prisma/client';
import { BaseEvent } from './base.event';
export interface UserClaimedEventData {
  bettor: string;
  amount: number;
  debateId: number;
  txHash: string;
}

export class UserClaimedEvent extends BaseEvent {
  data: UserClaimedEventData;

  constructor(dbEvent: Event) {
    super(dbEvent, EventType.UserClaimed);
    this.data = dbEvent.returnValues as UserClaimedEventData;
    this.data.txHash = dbEvent.txHash;
  }
}
