import { Event, EventType } from '@prisma/client';

export abstract class BaseEvent {
  eventType: EventType;
  dbEvent: Event;
  smartContractAddress: string;
  timestamp: Date;
  txHash: string;

  protected constructor(dbEvent: Event, eventType: EventType) {
    this.dbEvent = dbEvent;
    this.eventType = eventType;
    this.timestamp = dbEvent.timestamp;
    this.smartContractAddress = dbEvent.smartContractAddress;
    this.txHash = dbEvent.txHash;
  }
}
