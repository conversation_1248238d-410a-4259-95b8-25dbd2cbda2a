import { Event, EventType } from '@prisma/client';
import { BaseEvent } from './base.event';

export interface DebateCreatedEventData {
  debateId: number;
  agentAID: number;
  agentBID: number;
  platformFeePercentage: number;
  publicTimeStamp: number;
  startTimeStamp: number;
  sessionDuration: number;
}

export class DebateCreatedEvent extends BaseEvent {
  data: DebateCreatedEventData;

  constructor(dbEvent: Event) {
    super(dbEvent, EventType.DebateCreated);
    this.data = dbEvent.returnValues as DebateCreatedEventData;
  }
}
