import { Event, EventType } from '@prisma/client';
import { BaseEvent } from './base.event';

export interface DebateMarkAsRefundedEventData {
  debateId: number;
}

export class DebateMarkAsRefundedEvent extends BaseEvent {
  data: DebateMarkAsRefundedEventData;

  constructor(dbEvent: Event) {
    super(dbEvent, EventType.DebateMarkedRefundable);
    this.data = dbEvent.returnValues as DebateMarkAsRefundedEventData;
  }
}
