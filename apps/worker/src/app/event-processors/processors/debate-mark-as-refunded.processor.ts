import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  BattleMarkAsRefundedStatus,
  EventType,
  Prisma,
  PrismaClient
} from '@prisma/client';
import { DefaultArgs } from '@prisma/client/runtime/library';
import { DebateMarkAsRefundedEvent } from '../events/debate-mark-as-refunded.event';
import { BaseEventProcessor } from './base.processor';

@Injectable()
export class DebateMarkAsRefundedEventProcessor extends BaseEventProcessor<DebateMarkAsRefundedEvent> {
  protected readonly logger = new Logger(DebateMarkAsRefundedEventProcessor.name);
  constructor(private readonly emitter: EventEmitter2) {
    super();
  }

  protected override async prepare(
    event: DebateMarkAsRefundedEvent,
    prismaTransaction: Omit<
      PrismaClient<Prisma.PrismaClientOptions, never, DefaultArgs>,
      '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
    >
  ): Promise<void> {
    this.logger.debug(
      'DebateMarkAsRefundedEventProcessor::prepare',
      JSON.stringify(event)
    );

    const debate = await prismaTransaction.battle.findUniqueOrThrow({
      where: {
        id: event.data.debateId,
      },
    });

    const battle = await prismaTransaction.battle.update({
      where: {
        id: debate.id,
      },
      data: {
        markAsRefundedOnchainStatus: BattleMarkAsRefundedStatus.Completed,
      },
    });

    this.emitter.emit(EventType.DebateMarkedRefundable, battle);
  }
}
