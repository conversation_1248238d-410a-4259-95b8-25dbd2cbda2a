import { Injectable } from '@nestjs/common';
import { EventType } from '@prisma/client';
import { BaseEventProcessor } from './base.processor';
import { BaseEvent } from '../events/base.event';
import { DebateResolvedEventProcessor } from './debate-resolved.processor';
import { BetPlacedEventProcessor } from './bet-placed.processor';
import { UserClaimedEventProcessor } from './user-claimed.processor';
import { DebateUpdatedEventProcessor } from './debate-updated.processor';
import { DebateDeletedEventProcessor } from './debate-deleted.processor';
import { DebateCreatedEventProcessor } from './debate-created.processor';
import { DebateMarkAsRefundedEventProcessor } from './debate-mark-as-refunded.processor';
import { DebateRefundSucessfulEventProcessor } from './debate-refundsuccessful.processor';
import { UserRefundedEventProcessor } from './user-refunded.processor';

export type EventProcessorMapper = {
  readonly [eventType in EventType]: BaseEventProcessor<BaseEvent>;
};

@Injectable()
export class EventProcessorFactory {
  constructor(
    private readonly debateResolvedEventProcessor: DebateResolvedEventProcessor,
    private readonly betPlacedEventProcessor: BetPlacedEventProcessor,
    private readonly userClaimedEventProcessor: UserClaimedEventProcessor,
    private readonly debateCreatedEventProcessor: DebateCreatedEventProcessor,
    private readonly debateUpdatedEventProcessor: DebateUpdatedEventProcessor,
    private readonly debateDeletedEventProcessor: DebateDeletedEventProcessor,
    private readonly debateMarkAsRefundedEventProcessor: DebateMarkAsRefundedEventProcessor,
    private readonly debateRefundSuccessfulEventProcessor: DebateRefundSucessfulEventProcessor,
    private readonly userRefundedEventProcessor: UserRefundedEventProcessor
  ) {}

  get: EventProcessorMapper = {
    [EventType.DebateResolved]: this.debateResolvedEventProcessor,
    [EventType.BetPlaced]: this.betPlacedEventProcessor,
    [EventType.UserClaimed]: this.userClaimedEventProcessor,
    [EventType.DebateCreated]: this.debateCreatedEventProcessor,
    [EventType.DebateUpdated]: this.debateUpdatedEventProcessor,
    [EventType.DebateDeleted]: this.debateDeletedEventProcessor,
    [EventType.DebateMarkedRefundable]: this.debateMarkAsRefundedEventProcessor,
    [EventType.RefundSuccessful]: this.debateRefundSuccessfulEventProcessor,
    [EventType.UserRefunded]: this.userRefundedEventProcessor,
  };
}
