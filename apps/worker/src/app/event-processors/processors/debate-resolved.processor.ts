import { Injectable, Logger } from '@nestjs/common';
import { DebateResolvedEvent } from '../events/debate-resolved.event';
import { BaseEventProcessor } from './base.processor';
import {
  PrismaClient,
  Prisma,
  BattleResolvedOnchainStatus,
  OnChainTransactionStatus,
  EventType,
} from '@prisma/client';
import { DefaultArgs } from '@prisma/client/runtime/library';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class DebateResolvedEventProcessor extends BaseEventProcessor<DebateResolvedEvent> {
  protected readonly logger = new Logger(DebateResolvedEventProcessor.name);

  constructor(private readonly emitter: EventEmitter2) {
    super();
  }

  protected override async prepare(
    event: DebateResolvedEvent,
    prismaTransaction: Omit<
      PrismaClient<Prisma.PrismaClientOptions, never, DefaultArgs>,
      '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
    >
  ): Promise<void> {
    this.logger.debug(
      'DebateResolvedEventProcessor::prepare',
      JSON.stringify(event)
    );

    const onchainTxn = await prismaTransaction.onchainTransaction.findUnique({
      where: {
        txnHash: event.txHash,
      },
    });

    if (!onchainTxn) {
      throw new Error(
        `DebateResolvedEventProcessor::prepare::onChainTransaction not found for txHash: ${event.txHash}`
      );
    }

    const debate = await prismaTransaction.battle.findUnique({
      where: {
        id: onchainTxn.referenceId,
      },
    });

    if (!debate) {
      throw new Error(
        `DebateResolvedEventProcessor::prepare::debate not found for txHash: ${event.txHash}`
      );
    }

    const battle = await prismaTransaction.battle.update({
      where: {
        id: debate.id,
      },
      data: {
        resolved_onchain_status: BattleResolvedOnchainStatus.Completed,
        resolved_onchain_fail_count: 0,
      },
    });

    await prismaTransaction.onchainTransaction.update({
      where: {
        id: onchainTxn.id,
      },
      data: {
        status: OnChainTransactionStatus.Completed,
      },
    });

    this.emitter.emit(EventType.DebateResolved, battle);
  }
}
