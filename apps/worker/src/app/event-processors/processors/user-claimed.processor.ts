import { Injectable, Logger } from '@nestjs/common';
import { UserClaimedEvent } from '../events/user-claimed.event';
import { BaseEventProcessor } from './base.processor';
import { PrismaClient, Prisma, EventType } from '@prisma/client';
import { Decimal, DefaultArgs } from '@prisma/client/runtime/library';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class UserClaimedEventProcessor extends BaseEventProcessor<UserClaimedEvent> {
  protected readonly logger = new Logger(UserClaimedEventProcessor.name);

  constructor(private readonly emitter: EventEmitter2) {
    super();
  }

  protected override async prepare(
    event: UserClaimedEvent,
    prismaTransaction: Omit<
      PrismaClient<Prisma.PrismaClientOptions, never, DefaultArgs>,
      '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
    >
  ): Promise<void> {
    this.logger.debug(
      'UserClaimedEventProcessor::prepare',
      JSON.stringify(event)
    );
    try {
      const userClaimHistory: Prisma.UserClaimHistoryCreateInput = {
        battleId: event.data.debateId,
        userWallet: event.data.bettor,
        amount: new Decimal(event.data.amount).dividedBy(
          new Decimal(10).pow(18)
        ),
        txnHash: event.data.txHash,
      };

      const userClaim = await prismaTransaction.userClaimHistory.create({
        data: userClaimHistory,
      });

      this.emitter.emit(EventType.UserClaimed, userClaim);
    } catch (e) {
      this.logger.error(
        `UserClaimedEventProcessor::prepare::error: ${e.message}`
      );
      throw e;
    }
  }
}
