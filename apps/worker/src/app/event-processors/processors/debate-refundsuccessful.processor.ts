import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  BattleRefundedSuccessfulStatus,
  EventType,
  Prisma,
  PrismaClient
} from '@prisma/client';
import { DefaultArgs } from '@prisma/client/runtime/library';
import { DebateRefundSuccessfulEvent } from '../events/debate-refundsuccessful.event';
import { BaseEventProcessor } from './base.processor';

@Injectable()
export class DebateRefundSucessfulEventProcessor extends BaseEventProcessor<DebateRefundSuccessfulEvent> {
  protected readonly logger = new Logger(DebateRefundSucessfulEventProcessor.name);
  constructor(private readonly emitter: EventEmitter2) {
    super();
  }

  protected override async prepare(
    event: DebateRefundSuccessfulEvent,
    prismaTransaction: Omit<
      PrismaClient<Prisma.PrismaClientOptions, never, DefaultArgs>,
      '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
    >
  ): Promise<void> {
    this.logger.debug(
      'DebateRefundSucessfulEventProcessor::prepare',
      JSON.stringify(event)
    );

    const debate = await prismaTransaction.battle.findUniqueOrThrow({
      where: {
        id: event.data.debateId,
      },
    });

    const battle = await prismaTransaction.battle.update({
      where: {
        id: debate.id,
      },
      data: {
        refundedOnchainStatus: BattleRefundedSuccessfulStatus.Completed,
      },
    });

    this.emitter.emit(EventType.RefundSuccessful, battle);
  }
}
