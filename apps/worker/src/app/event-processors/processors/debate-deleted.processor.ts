import { Injectable, Logger } from '@nestjs/common';
import { DebateDeletedEvent } from '../events/debate-deleted.event';
import { BaseEventProcessor } from './base.processor';
import { PrismaClient, Prisma, EventType } from '@prisma/client';
import { DefaultArgs } from '@prisma/client/runtime/library';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class DebateDeletedEventProcessor extends BaseEventProcessor<DebateDeletedEvent> {
  protected readonly logger = new Logger(DebateDeletedEventProcessor.name);

  constructor(private readonly emitter: EventEmitter2) {
    super();
  }

  protected override async prepare(
    event: DebateDeletedEvent,
    prismaTransaction: Omit<
      PrismaClient<Prisma.PrismaClientOptions, never, DefaultArgs>,
      '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
    >
  ): Promise<void> {
    this.logger.debug(
      'DebateDeletedEventProcessor::prepare',
      JSON.stringify(event)
    );

    const debate = await prismaTransaction.battle.findUniqueOrThrow({
      where: {
        id: event.data.debateId,
      },
    });

    const battle = await prismaTransaction.battle.update({
      where: {
        id: debate.id,
      },
      data: {
        deleted_at: new Date(),
      },
    });

    this.emitter.emit(EventType.DebateDeleted, battle);
  }
}
