import { Injectable, Logger } from '@nestjs/common';
import { BaseEventProcessor } from './base.processor';
import { BetPlacedEvent } from '../events/bet-placed.event';
import { DebateResolvedEventProcessor } from './debate-resolved.processor';
import { PrismaClient, Prisma, EventType } from '@prisma/client';
import { Decimal, DefaultArgs } from '@prisma/client/runtime/library';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class BetPlacedEventProcessor extends BaseEventProcessor<BetPlacedEvent> {
  protected readonly logger = new Logger(DebateResolvedEventProcessor.name);

  constructor(private readonly emitter: EventEmitter2) {
    super();
  }

  protected override async prepare(
    event: BetPlacedEvent,
    prismaTransaction: Omit<
      PrismaClient<Prisma.PrismaClientOptions, never, DefaultArgs>,
      '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
    >
  ): Promise<void> {
    this.logger.debug(
      'BetPlacedEventProcessor::prepare',
      JSON.stringify(event)
    );
    try {
      const userBet: Prisma.BattleUserBetCreateInput = {
        battleId: event.data.debateId,
        userWallet: event.data.bettor,
        betAmount: new Decimal(event.data.amount).dividedBy(
          new Decimal(10).pow(18)
        ),
        agentId: event.data.chosenAgentId,
        txnHash: event.txHash,
      };

      await prismaTransaction.user.upsert({
        where: { walletAddress: event.data.bettor },
        update: {}, 
        create: { walletAddress: event.data.bettor },
      });

      const battleUserBet = await prismaTransaction.battleUserBet.create({
        data: userBet,
      });

      this.emitter.emit(EventType.BetPlaced, battleUserBet);
    } catch (e) {
      this.logger.error(
        `BetPlacedEventProcessor::prepare::error: ${e.message}`
      );
      throw e;
    }
  }
}
