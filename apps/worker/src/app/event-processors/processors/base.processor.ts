import { Logger } from '@nestjs/common';
import { PrismaClient, Prisma, EventStatus } from '@prisma/client';
import { DefaultArgs } from '@prisma/client/runtime/library';
import { BaseEvent } from '../events/base.event';
import { waitForExecutionOrTimeout } from '../../transaction-processors/const/transaction.const';

export abstract class BaseEventProcessor<E extends BaseEvent> {
  protected readonly logger: Logger = new Logger(BaseEventProcessor.name);

  protected async prepare(
    event: E,
    _: Omit<
      PrismaClient<Prisma.PrismaClientOptions, never, DefaultArgs>,
      '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
    >
  ) {
    throw new Error(
      `No implementation found for eventType='${event.eventType}'`
    );
  }

  public async execute(
    event: E,
    t: Omit<
      PrismaClient<Prisma.PrismaClientOptions, never, DefaultArgs>,
      '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
    >
  ) {
    try {
      await waitForExecutionOrTimeout(this.prepare.bind(this, event, t));
      await t.event.update({
        where: {
          id: event.dbEvent.id,
        },
        data: {
          status: EventStatus.Completed,
        },
      });
    } catch (e) {
      await t.event.update({
        where: { id: event.dbEvent.id },
        data: {
          status: EventStatus.Failed,
          updatedAt: new Date(),
          processErrorMsg: e.message,
        },
      });
      this.logger.error(
        `eventType='${event.eventType}' (txHash='${event.dbEvent.txHash}') processed with errors err='${e.message}'`
      );
      throw e;
    }
  }
}
