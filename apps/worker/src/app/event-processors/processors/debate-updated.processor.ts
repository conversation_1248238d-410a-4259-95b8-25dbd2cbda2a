import { Injectable, Logger } from '@nestjs/common';
import { DebateUpdatedEvent } from '../events/debate-updated.event';
import { BaseEventProcessor } from './base.processor';
import { PrismaClient, Prisma, EventType } from '@prisma/client';
import { DefaultArgs } from '@prisma/client/runtime/library';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class DebateUpdatedEventProcessor extends BaseEventProcessor<DebateUpdatedEvent> {
  protected readonly logger = new Logger(DebateUpdatedEventProcessor.name);
  constructor(private readonly emitter: EventEmitter2) {
    super();
  }

  protected override async prepare(
    event: DebateUpdatedEvent,
    prismaTransaction: Omit<
      PrismaClient<Prisma.PrismaClientOptions, never, DefaultArgs>,
      '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
    >
  ): Promise<void> {
    this.logger.debug(
      'DebateUpdatedEventProcessor::prepare',
      JSON.stringify(event)
    );

    const debate = await prismaTransaction.battle.findUniqueOrThrow({
      where: {
        id: event.data.debateId,
      },
    });

    const battle = await prismaTransaction.battle.update({
      where: {
        id: debate.id,
      },
      data: {
        agent1_id: event.data.agentAID,
        agent2_id: event.data.agentBID,
        platform_fee: event.data.platformFeePercentage / 100, // %
        public_at: new Date(event.data.publicTimeStamp * 1000),
        start_at: new Date(event.data.startTimeStamp * 1000),
        duration: event.data.sessionDuration / 60,
      },
    });

    this.emitter.emit(EventType.DebateUpdated, battle);

  }
}
