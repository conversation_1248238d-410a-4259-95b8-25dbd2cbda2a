import { EventEmitter2 } from '@nestjs/event-emitter';
import { Injectable, Logger } from '@nestjs/common';
import { BaseEventProcessor } from './base.processor';
import { DebateCreatedEvent } from '../events/debate-created.event';
import {
  PrismaClient,
  Prisma,
  BattleCreatedOnchainStatus,
  EventType,
} from '@prisma/client';
import { DefaultArgs } from '@prisma/client/runtime/library';

@Injectable()
export class DebateCreatedEventProcessor extends BaseEventProcessor<DebateCreatedEvent> {
  protected readonly logger = new Logger(DebateCreatedEventProcessor.name);
  constructor(private readonly emitter: EventEmitter2) {
    super();
  }

  protected override async prepare(
    event: DebateCreatedEvent,
    prismaTransaction: Omit<
      PrismaClient<Prisma.PrismaClientOptions, never, DefaultArgs>,
      '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
    >
  ): Promise<void> {
    this.logger.debug(
      'DebateCreatedEventProcessor::prepare',
      JSON.stringify(event)
    );

    const debate = await prismaTransaction.battle.findUniqueOrThrow({
      where: {
        id: event.data.debateId,
      },
    });

    const battle = await prismaTransaction.battle.update({
      where: {
        id: debate.id,
      },
      data: {
        created_onchain_status: BattleCreatedOnchainStatus.Successful,
      },
    });

    this.emitter.emit(EventType.DebateCreated, battle);
  }
}
