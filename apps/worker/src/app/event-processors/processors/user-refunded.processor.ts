import { Injectable, Logger } from '@nestjs/common';
import { UserClaimedEvent } from '../events/user-claimed.event';
import { BaseEventProcessor } from './base.processor';
import { PrismaClient, Prisma, EventType, BattleRefundedSuccessfulStatus } from '@prisma/client';
import { Decimal, DefaultArgs } from '@prisma/client/runtime/library';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { UserRefundedEvent } from '../events/user-refunded.event';

@Injectable()
export class UserRefundedEventProcessor extends BaseEventProcessor<UserRefundedEvent> {
  protected readonly logger = new Logger(UserRefundedEventProcessor.name);

  constructor(private readonly emitter: EventEmitter2) {
    super();
  }

  protected override async prepare(
    event: UserClaimedEvent,
    prismaTransaction: Omit<
      PrismaClient<Prisma.PrismaClientOptions, never, DefaultArgs>,
      '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
    >
  ): Promise<void> {
    this.logger.debug(
      'UserRefundedEventProcessor::prepare',
      JSON.stringify(event)
    );
    try {
      const userRefundHistory: Prisma.UserRefundHistoryCreateInput = {
        battleId: event.data.debateId,
        userWallet: event.data.bettor,
        amount: new Decimal(event.data.amount).dividedBy(
          new Decimal(10).pow(18)
        ),
        txnHash: event.txHash,
      };

      const UserRefunded = await prismaTransaction.userRefundHistory.create({
        data: userRefundHistory,
      });

      await prismaTransaction.battle.update({
        where: {
          id: event.data.debateId,
        },
        data: {
          refundedOnchainStatus: BattleRefundedSuccessfulStatus.InProgress,
        },
      });

      this.emitter.emit(EventType.UserRefunded, UserRefunded);
    } catch (e) {
      this.logger.error(
        `UserRefundedEventProcessor::prepare::error: ${e.message}`
      );
      throw e;
    }
  }
}
