{"name": "worker", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/worker/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["node-env=production"]}, "configurations": {"development": {"args": ["node-env=development"]}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "worker:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "worker:build:development"}, "production": {"buildTarget": "worker:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}