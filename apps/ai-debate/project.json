{"name": "ai-debate", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/ai-debate/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["node-env=production"]}, "configurations": {"development": {"args": ["node-env=development"]}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "ai-debate:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "ai-debate:build:development"}, "production": {"buildTarget": "ai-debate:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}