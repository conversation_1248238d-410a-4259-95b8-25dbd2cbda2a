// SPDX-License-Identifier: SEE LICENSE IN LICENSE
pragma solidity ^0.8.0;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "hardhat/console.sol";


contract DebateBetting is Initializable, OwnableUpgradeable {
    IERC20 public myToken;
    event BetPlaced(
        uint256 indexed debateId,
        address indexed bettor,
        uint8 chosenAgentId,
        uint256 amount
    );

    event DebateResolved(
        uint256 indexed debateId,
        uint8 winnAgentId
    );

    struct Debate {
        bool isResolved;
        uint8 winAgentId; // 1 = agent1, 2 = agent2
        uint256 totalAgent1BetAmount;
        uint256 totalAgent2BetAmount;
    }

    struct Bet {
        uint256 debateId;
        address bettor;
        uint256 amount;
        uint8 chosenAgentId; // 1 for agent1, 2 for agent2
    }

    mapping(uint256 => Debate) public debates;
    mapping(uint256 => mapping (address => Bet)) public bets;
    mapping(uint256 => address[]) public debateBettors;

    // constructor(address initialOwner, IERC20 _myToken)
    // Ownable(initialOwner) {
    //     myToken = _myToken;
    // }

    function initialize(address initialOwner, IERC20 _myToken) public initializer {
        __Ownable_init(initialOwner);
        myToken = _myToken;
    }

    function placeBet(
        uint256 _debateId,
        uint256 _amount,
        uint8 _chosenAgent
        ) external {
        require(_chosenAgent == 1 || _chosenAgent == 2, "Invalid agent");
        require(_amount > 0, "Amount must be greater than 0");

        console.log("Place bet");
        Debate storage debate = debates[_debateId];
        require(!debate.isResolved, "Debate is resolved");

        console.log("Debate is not resolved");

        require(myToken.transferFrom(msg.sender, address(this), _amount), "Transfer failed");
        console.log("Transfer successful");

        bets[_debateId][msg.sender] = Bet(_debateId, msg.sender, _amount, _chosenAgent);
        console.log("Bet added");

        if (_chosenAgent == 1) {
            debate.totalAgent1BetAmount += _amount;
        } else {
            debate.totalAgent2BetAmount += _amount;
        }

        console.log("Total bet amount updated");

        debateBettors[_debateId].push(msg.sender);
        console.log("Bettor added to debate");

        emit BetPlaced(_debateId, msg.sender, _chosenAgent, _amount);
    }

    function resolveDebate(uint256 _debateId, uint8 winAgentId) external onlyOwner {
        require(winAgentId == 1 || winAgentId == 2, "Invalid agent");

        Debate storage debate = debates[_debateId];
        require(!debate.isResolved, "Debate is already resolved");

        debate.isResolved = true;
        debate.winAgentId = winAgentId;

        uint256 totalWinAmount = winAgentId == 1 ? debate.totalAgent1BetAmount : debate.totalAgent2BetAmount;
        uint256 totalLoseAmount = winAgentId == 1 ? debate.totalAgent2BetAmount : debate.totalAgent1BetAmount;

        for (uint256 i = 0; i < debateBettors[_debateId].length; i++) {
            address bettor = debateBettors[_debateId][i];
            Bet memory bet = bets[_debateId][bettor];
            if (bet.chosenAgentId != winAgentId) {
                continue;
            }
            uint256 proportionalShare = (bet.amount / totalWinAmount);
            uint256 winAmount = proportionalShare * totalLoseAmount;
            require(myToken.transfer(bettor, bet.amount + winAmount), "Transfer failed");
        }

        emit DebateResolved(_debateId, winAgentId);
    }

    function getDebateInfo(uint256 _debateId) external view returns (Debate memory) {
        Debate storage debate = debates[_debateId];
        return debate;
    }
}