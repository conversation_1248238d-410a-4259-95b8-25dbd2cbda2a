{"manifestVersion": "3.2", "proxies": [{"address": "0x444a718A11F9bC277E014F7129Bd68f3Ea1b352f", "txHash": "0x808704a7a1a4e6541b42d58da724dccc1ecbcb2a8a99db7fe11122411427683a", "kind": "transparent"}, {"address": "0xAa34B1B8fFDFaF5FD42f13f4F872C732643F5a72", "txHash": "0x0bc9b58bbf2104420aeb6603e7ebcabafc5c6d43bf8efa7ca9d599176643e695", "kind": "transparent"}], "impls": {"3097e459c83fa5b16bb283de011e5db9fee91bd27a4204baa642cb31a3f3f866": {"address": "0x1f6ac3a77364F19673e1422dCac49EF4c06A248c", "txHash": "0xb76b9e05be8150e90bf51aca95c315cc107a9ac9430bd5c070873e4aebae3046", "layout": {"solcVersion": "0.8.28", "storage": [{"label": "myToken", "offset": 0, "slot": "0", "type": "t_contract(IERC20)1372", "contract": "DebateBetting", "src": "contracts/DebateBetting.sol:10"}, {"label": "debates", "offset": 0, "slot": "1", "type": "t_mapping(t_uint256,t_struct(Debate)1465_storage)", "contract": "DebateBetting", "src": "contracts/DebateBetting.sol:39"}, {"label": "bets", "offset": 0, "slot": "2", "type": "t_mapping(t_uint256,t_mapping(t_address,t_struct(Bet)1474_storage))", "contract": "DebateBetting", "src": "contracts/DebateBetting.sol:40"}, {"label": "debateBettors", "offset": 0, "slot": "3", "type": "t_mapping(t_uint256,t_array(t_address)dyn_storage)", "contract": "DebateBetting", "src": "contracts/DebateBetting.sol:41"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)73_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)13_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_array(t_address)dyn_storage": {"label": "address[]", "numberOfBytes": "32"}, "t_contract(IERC20)1372": {"label": "contract IERC20", "numberOfBytes": "20"}, "t_mapping(t_address,t_struct(Bet)1474_storage)": {"label": "mapping(address => struct DebateBetting.Bet)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_array(t_address)dyn_storage)": {"label": "mapping(uint256 => address[])", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_mapping(t_address,t_struct(Bet)1474_storage))": {"label": "mapping(uint256 => mapping(address => struct DebateBetting.Bet))", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_struct(Debate)1465_storage)": {"label": "mapping(uint256 => struct DebateBetting.Debate)", "numberOfBytes": "32"}, "t_struct(Bet)1474_storage": {"label": "struct DebateBetting.Bet", "members": [{"label": "debateId", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "bettor", "type": "t_address", "offset": 0, "slot": "1"}, {"label": "amount", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "chosenAgentId", "type": "t_uint8", "offset": 0, "slot": "3"}], "numberOfBytes": "128"}, "t_struct(Debate)1465_storage": {"label": "struct DebateBetting.Debate", "members": [{"label": "isResolved", "type": "t_bool", "offset": 0, "slot": "0"}, {"label": "winAgentId", "type": "t_uint8", "offset": 1, "slot": "0"}, {"label": "totalAgent1BetAmount", "type": "t_uint256", "offset": 0, "slot": "1"}, {"label": "totalAgent2BetAmount", "type": "t_uint256", "offset": 0, "slot": "2"}], "numberOfBytes": "96"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "namespaces": {"erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}}}}