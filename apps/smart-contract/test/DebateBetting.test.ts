import { DebateBetting } from "../typechain-types"

describe("DebateBetting", () => {
    let DebateBetting: DebateBetting
    let owner: any

    before(async () => {
        const DebateBettingFactory = await ethers.getContractFactory("DebateBetting")
        DebateBetting = await DebateBettingFactory.deploy()
        await DebateBetting.deployed()
        owner = await DebateBetting.owner()
    })
})