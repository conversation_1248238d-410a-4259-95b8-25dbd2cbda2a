import { ethers, upgrades } from 'hardhat';

async function main() {
  try {
    const [deployer] = await ethers.getSigners();
    console.log('Deploying contracts with the account:', deployer);

    const myToken = '******************************************';

    const debateBettingContract = await ethers.getContractFactory(
      'DebateBetting'
    );

    const proxy = await upgrades.deployProxy(debateBettingContract, [
      deployer.address,
      myToken,
    ]);
    console.log('MyCollectible deploying to:', await proxy.getAddress());
    await proxy.waitForDeployment();
    console.log('MyCollectible deployed to:', await proxy.getAddress());
  } catch (error) {
    console.error(error);
    process.exit(1);
  }
}

main();
