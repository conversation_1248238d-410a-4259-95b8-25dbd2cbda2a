import { ethers } from "hardhat";
import { INITIAL_TOKEN_SUPPLY } from "../env";


async function main() {
    try {
        // Get the deployer's signer
        const [deployer] = await ethers.getSigners();
        console.log('Deploying contracts with the account:', deployer);
        console.log(
        'Account balance:',
        (await deployer.provider.getBalance(deployer.address)).toString());
        
        console.log('Deploying MyToken...');
        const MyToken = await ethers.getContractFactory("MyToken");
        const myToken = await MyToken.deploy(deployer.address, INITIAL_TOKEN_SUPPLY);
        await myToken.waitForDeployment();

        // Get the deployed contract address
        const contractAddress = await myToken.getAddress();

        console.log('MyToken deployed to:', contractAddress);
        console.log('Token Name:', await myToken.name());
        console.log('Token Symbol:', await myToken.symbol());
        console.log('Total Supply:', (await myToken.totalSupply()).toString());
        // console.log('Owner:', await myToken.owner());
    } catch (error) {
        console.error(error);
        process.exit(1);
    }
}   

// Execute the deployment
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });