{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:base"], "packageRules": [{"groupName": "@subsquid", "matchPackagePatterns": ["^@subsquid/"], "matchUpdateTypes": ["minor", "patch", "pin", "digest"]}, {"matchPackagePatterns": ["*"], "excludePackagePatterns": ["^@subsquid/"], "enabled": false}], "automerge": true, "automergeType": "pr", "automergeStrategy": "squash", "ignoreTests": true}