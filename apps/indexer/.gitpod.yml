# This configuration file was automatically generated by Gitpod.
# Please adjust to your needs (see https://www.gitpod.io/docs/introduction/learn-gitpod/gitpod-yaml)
# and commit this file to your remote git repository to share the goodness with others.

# Learn more from ready-to-use templates: https://www.gitpod.io/docs/introduction/getting-started/quickstart
github:
  prebuilds:
    # enable for the master/default branch (defaults to true)
    master: true
    # enable for all branches in this repo (defaults to false)
    branches: false
    # enable for pull requests coming from this repo (defaults to true)
    pullRequests: true
    # add a check to pull requests (defaults to true)
    addCheck: true
    # add a "Review in Gitpod" button as a comment to pull requests (defaults to false)
    addComment: false

tasks:
  - init: |
      npm i
      npm i -g @subsquid/cli
      docker compose pull
      gp sync-done setup
  - name: DB
    command: |
      gp sync-await setup
      sqd up
  - name: GraphQL API
    command: |
      gp sync-await setup
      sqd serve
  - command: |
      gp ports await 4350 
      gp preview $(gp url 4350)/graphql
  - name: Squid procesor
    command: |
      gp open src/processor.ts
      gp sync-await setup
      sqd build
      gp ports await 23798
      sqd process
