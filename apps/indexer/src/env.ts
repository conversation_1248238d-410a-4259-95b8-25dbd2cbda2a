export const DB_URL =
  process.env.DB_URL ||
  'postgresql://postgres:password@localhost:5432/postgres';

export const GQL_PORT = 4350;

export const GATEWAY_ENDPOINT = '';

export const RPC_ENDPOINT =
  process.env.RPC_ENDPOINT || 'https://evmrpc-testnet.0g.ai';
console.debug(`RPC_ENDPOINT: ${RPC_ENDPOINT}`);

export const RPC_RATE_LIMIT = Number(process.env.RPC_RATE_LIMIT) || 30;

export const BLOCK_CONFIRM = Number(process.env.BLOCK_CONFIRM) || 1;

export const FROM_BLOCK = Number(process.env.FROM_BLOCK) || 2892897;
console.debug(`FROM_BLOCK: ${FROM_BLOCK}`);

export const DEBATE_BETTING_CONTRACT_ADDRESS =
  process.env.DEBATE_BETTING_CONTRACT_ADDRESS ||
  'DEBATE_BETTING_CONTRACT_ADDRESS';
console.debug(
  `DEBATE_BETTING_CONTRACT_ADDRESS: ${DEBATE_BETTING_CONTRACT_ADDRESS}`
);
