import {
  Entity as Entity_,
  Column as Column_,
  PrimaryColumn as PrimaryColumn_,
  StringColumn as StringColumn_,
  Index as Index_,
  JSONColumn as J<PERSON>NColumn_,
  IntColumn as IntColumn_,
  DateTimeColumn as DateTimeColumn_,
} from '@subsquid/typeorm-store';

@Entity_('BATTLE_Event')
export class Event {
  constructor(props?: Partial<Event>) {
    Object.assign(this, props);
  }

  @PrimaryColumn_()
  id!: string;

  @StringColumn_({ nullable: false, name: 'status' })
  status!: string;

  @StringColumn_({ nullable: false, name: 'name' })
  name!: string;

  @StringColumn_({ nullable: false, name: 'smartContractAddress' })
  smartContractAddress!: string;

  @Index_()
  @StringColumn_({ nullable: false, name: 'txHash' })
  txHash!: string;

  @JSONColumn_({ nullable: true, name: 'returnValues' })
  returnValues!: unknown | undefined | null;

  @IntColumn_({ nullable: false, name: 'blockNumber' })
  blockNumber!: number;

  @DateTimeColumn_({ nullable: false, name: 'timestamp' })
  timestamp!: Date;

  @IntColumn_({ nullable: false, name: 'logIndex' })
  logIndex!: number;

  @DateTimeColumn_({ nullable: false, name: 'createdAt' })
  createdAt!: Date;

  @DateTimeColumn_({ nullable: false, name: 'updatedAt' })
  updatedAt!: Date;
}
