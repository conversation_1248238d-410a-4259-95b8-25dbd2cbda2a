{"name": "squid-evm-template", "private": true, "scripts": {"build": "rm -rf lib && tsc"}, "dependencies": {"@subsquid/evm-abi": "^0.3.0", "@subsquid/evm-processor": "^1.19.1", "@subsquid/graphql-server": "^4.6.0", "@subsquid/typeorm-migration": "^1.3.0", "@subsquid/typeorm-store": "^1.5.1", "@subsquid/util-internal": "^3.2.0", "dotenv": "^16.4.5", "pg": "^8.12.0", "typeorm": "^0.3.20"}, "devDependencies": {"@subsquid/evm-typegen": "^4.2.0", "@subsquid/typeorm-codegen": "^2.0.1", "@types/node": "^20.14.8", "typescript": "~5.5.2"}}