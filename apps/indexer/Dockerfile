# I: Base Image
FROM node:18.19.0-alpine@sha256:b1a0356f7d6b86c958a06949d3db3f7fb27f95f627aa6157cb98bc65c801efa2
WORKDIR /usr/src/app

# II: Copy artifacts
# Adjusted paths relative to the Dockerfile location
COPY ./node_modules ./node_modules
COPY ./lib ./lib

# Step 14: Set environment variables
ENV NODE_ENV=production

# Step 16: Define the command to run your app
CMD ["node", "-r", "dotenv/config", "lib/main.js"]
